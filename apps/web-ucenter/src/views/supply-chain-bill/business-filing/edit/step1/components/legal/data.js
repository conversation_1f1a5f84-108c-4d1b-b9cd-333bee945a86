import { LongTerm } from '@dict';

export const formListFn = function (formData) {
	if (!formData) return [];
	return [
		{ id: 'legalPersonName', name: '法定代表人姓名', attributes: { disabled: true } },
		{ type: 'text' },
		{ id: 'legalPersonIdCode', name: '法定代表人证件号码', validate: 'isIdentityId', attributes: { disabled: true } },
		{ id: 'legalCertificateDate', name: '身份证有效期', type: 'radio', list: LongTerm.data, value: 0 }, // json
		{ id: 'legalCertificateStartDate', name: '身份证有效期开始时间', type: 'date' }, // json
		{
			id: 'legalCertificateEndDate',
			name: '身份证有效期结束时间',
			type: 'date',
			relation: { id: 'legalCertificateDate', val: 0 },
		}, // json
		{ type: 'text', relation: { id: 'legalCertificateDate', noVal: 0 } },
		{
			id: 'legalPersonPhone',
			name: '法人手机号码',
			validate: 'phone',
			attributes: { disabled: formData.identicalPerson == 1 },
		},
		{
			id: 'legalPersonVerifyCode',
			name: '短信验证码',
			slot: 'legalPersonVerifyCode',
			relation: {
				id: 'legalPersonPhone',
				val: (val) => {
					return val != formData.phone && formData.identicalPerson == 0;
				},
			},
		},
	];
};
