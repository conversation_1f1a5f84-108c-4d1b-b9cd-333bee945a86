import { yesOrNoEnum, LongTerm, HlyIndustry } from '@dict';

export const formListFn = function () {
	return [
		{
			id: 'name',
			name: '企业名称',
			attributes: { disabled: true },
			rowSlot: 'name',
			css: 'basis-full w-50',
		},
		{ id: 'unifiedSocialCreditCode', name: '统一社会信用代码', attributes: { disabled: true }, css: 'basis-full w-50' },
		{ id: 'foundAddress', name: '企业地址', slot: 'foundAddress' },
		{ id: 'detailedAddress', name: '具体地址', max: '150' },
		{ id: 'isPrivateEnterprise', name: '是否为民营企业', type: 'radio', list: yesOrNoEnum.data, value: 0 },
		{ id: 'ifBusiness', name: '经营期限', type: 'radio', list: LongTerm.data, value: 0 },
		{ id: 'businessStartTime', name: '经营期限开始日期', type: 'date' },
		{ id: 'businessEndTime', name: '经营期限结束日期', type: 'date', relation: { id: 'ifBusiness', val: 0 } },
		{ type: 'text', relation: { id: 'ifBusiness', noVal: 0 } },
		{ id: 'industryType', name: '企业所属行业', dicKey: 'HlyIndustry', filterable: true },
		{
			id: 'registeredCapital',
			name: '注册资本',
			append: '万元',
			validate: {
				type: 'number',
				minus: true, // 是否允许负数（可选，默认false）
				int: 10,
				decimal: 2,
			},
		},
	];
};
