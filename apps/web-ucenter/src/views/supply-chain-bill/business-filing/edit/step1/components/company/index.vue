<template>
	<div class="co-single-form">
		<co-form ref="formRef" :form-list="formList" :form-data="formData" :dictConfig="dictConfig" :labelWidth="labelWidth">
			<template #foundAddress="{ data }">
				<pro-province-selector :checkStrictly="false" v-model="data.foundAddress" @region-change="(regionData) => (data.address = regionData.regionName)" />
			</template>
		</co-form>
	</div>
</template>

<script setup>
import ProProvinceSelector from '@components/pro-province-selector';

import { ref } from 'vue';
import { formListFn } from './data.js';
import * as dicApi from '@dict';
import { useDicts } from '/@/hooks/useDicts';
import { HlyIndustry } from '@dict';

const { verifyData } = inject('verify');

const existText = ref('');
const formRef = ref();
const formList = ref([]);
const formData = ref({});
const labelWidth = ref('175px');
const dictConfig = useDicts(dicApi, HlyIndustry);
formList.value = formListFn();
existText.value = '当前企业已认证，请联系管理员（张*林 156****0528）为您创建专属账号';
watchEffect(() => {
	formData.value = {
		...verifyData.value,
	};
});

async function getData() {
	return await formRef.value.getFormData();
}

defineExpose({
	getData,
});
</script>

<style lang="scss">
.co-single-form {
	.el-form {
		display: flex;
		flex-wrap: wrap;
	}

	.w-50 {
		.el-form-item__content > div {
			flex-basis: calc(50% - 87.5px);
		}
	}

	.w-30 {
		.el-form-item__content > div {
			flex-basis: 235px;
		}
	}

	.el-form-item {
		flex: 0 0 50%;
	}

	.co-form-block {
		flex: 0 0 100%;
	}

	.basis-full {
		flex-basis: 100%;
	}
}
</style>
