<template>
	<div class="enterprise-improve">
		<el-form ref="enterpriseImproveFromRef" label-position="top" :model="enterpriseImproveData" :rules="enterpriseImproveRules" inline>
			<v-ele-detail :config-data="configData">
				<template #customDecenterprise>
					<el-row :gutter="20" class="form-item-content">
						<el-col :span="8">
							<el-form-item label="企业名称：" prop="enterpriseName">
								<el-input placeholder="请输入企业名称" v-model="enterpriseImproveData.enterpriseName" disabled />
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="统一社会信用代码：" prop="ecode">
								<el-input placeholder="请输入统一社会信用代码" v-model="enterpriseImproveData.ecode" disabled />
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item label="法定代表人：" prop="legalPersonName">
								<el-input placeholder="请输入法定代表人" v-model="enterpriseImproveData.legalPersonName" disabled />
							</el-form-item>
						</el-col>
					</el-row>
				</template>
				<template #customDecother>
					<el-row :gutter="20" class="form-item-content">
						<el-col :span="6">
							<el-form-item class="license-class" label="营业执照：" prop="businessLicense">
								<UploadImg :id="enterpriseImproveData.businessLicense" :fileSize="10" width="200px" @change="(data:Object) => changeImage(data, 'businessLicense')" height="200px" />
							</el-form-item>
						</el-col>
						<el-col :span="18">
							<el-row :gutter="20">
								<el-col :span="12">
									<el-form-item label="企业类型：" prop="corpTp">
										<el-select placeholder="请选择企业类型" v-model="enterpriseImproveData.corpTp">
											<el-option v-for="item in corpTpList" :key="item.value" :value="item.value" :label="item.label"></el-option>
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="注册地区：" prop="registerRegion">
										<el-cascader
											ref="officeAddress"
											disabled
											v-model="enterpriseImproveData.registerRegion"
											:options="areaDataNoHasArea"
											style="width: 100%"
											placeholder="请选择"
											class="select-style"
											:props="{
												value: 'dicValue',
												label: 'dicName',
												children: 'children',
											}"
											@change="onClickOfficeAreaChange"
										/>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="注册地址：" prop="registerAddress">
										<el-input placeholder="请输入注册地址" v-model="enterpriseImproveData.registerAddress" />
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="企业规模：" prop="corpScale">
										<el-select placeholder="请选择企业规模" v-model="enterpriseImproveData.corpScale">
											<el-option v-for="item in corpScaleList" :key="item.value" :value="item.value" :label="item.label"></el-option>
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :span="24">
									<el-form-item label="营业执照有效期：" prop="termStartDate">
										<el-col style="padding-left: 0" :span="11">
											<el-date-picker type="date" v-model="enterpriseImproveData.termStartDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD HH:mm:ss" placeholder="开始时间" />
										</el-col>
										<el-col :span="2">
											<el-radio label="长期有效" v-model="enterpriseImproveData.termPermanent" value="1" @change="updatePermanent('termPermanent', 'termEndDate')" />
										</el-col>
										<el-col style="padding-right: 0" :span="11">
											<el-date-picker
												type="date"
												v-model="enterpriseImproveData.termEndDate"
												@change="updatePermanent('termEndDate', 'termPermanent', '0')"
												format="YYYY-MM-DD"
												value-format="YYYY-MM-DD HH:mm:ss"
												placeholder="结束时间"
											/>
										</el-col>
									</el-form-item>
								</el-col>
								<el-col :span="24">
									<el-form-item label="经营范围：" prop="businessScope">
										<el-input placeholder="请输入经营范围" type="textarea" :autosize="{ minRows: 2, maxRows: 6 }" v-model="enterpriseImproveData.businessScope" />
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="行业分类：" prop="indCls">
										<el-select placeholder="请选择行业分类" v-model="enterpriseImproveData.indCls">
											<el-option v-for="item in indClsList" :key="item.value" :value="item.value" :label="item.label"></el-option>
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="是否涉农：" prop="drAcg">
										<el-select placeholder="请选择是否涉农" v-model="enterpriseImproveData.drAcg">
											<el-option v-for="item in whetherList" :key="item.value" :value="item.value" :label="item.label"></el-option>
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="是否绿色：" prop="isGreen">
										<el-select placeholder="请选择是否绿色" v-model="enterpriseImproveData.isGreen">
											<el-option v-for="item in whetherList" :key="item.value" :value="item.value" :label="item.label"></el-option>
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="是否科技：" prop="isTech">
										<el-select placeholder="请选择是否科技" v-model="enterpriseImproveData.isTech">
											<el-option v-for="item in whetherList" :key="item.value" :value="item.value" :label="item.label"></el-option>
										</el-select>
									</el-form-item>
								</el-col>
							</el-row>
						</el-col>
					</el-row>
				</template>
				<template #customDeclegalPerson>
					<el-row :gutter="20" class="form-item-content">
						<el-col :span="6">
							<el-row>
								<el-col :span="24">
									<el-form-item label="证件类型：" prop="legalPersonIdType">
										<el-select placeholder="请选择证件类型" v-model="enterpriseImproveData.legalPersonIdType" disabled>
											<el-option value="DC01" label="身份证"></el-option>
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :span="24">
									<el-form-item label="证件上传：" class="orgCode-class" prop="legalCertificateFront">
										<UploadImg :id="enterpriseImproveData.legalCertificateFront" :fileSize="10" width="160px" @change="(data:Object) => changeImage(data, 'legalCertificateFront')" height="160px" />
										<UploadImg :id="enterpriseImproveData.legalCertificateBack" :fileSize="10" width="160px" @change="(data:Object) => changeImage(data, 'legalCertificateBack')" height="160px" />
									</el-form-item>
								</el-col>
							</el-row>
						</el-col>
						<el-col :span="18">
							<el-row :gutter="20">
								<el-col :span="12">
									<el-form-item label="法人证件号码：" prop="legalPersonIdCode">
										<el-input v-model="enterpriseImproveData.legalPersonIdCode" placeholder="请输入法人证件号码" disabled />
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="法人手机号：" prop="legalPersonPhone">
										<el-input v-model="enterpriseImproveData.legalPersonPhone" placeholder="请输入法人手机号" disabled />
									</el-form-item>
								</el-col>
								<el-col :span="24">
									<el-form-item label="法人证件有效期：" prop="legalPersonStartDate">
										<el-col style="padding-left: 0" :span="11">
											<el-date-picker v-model="enterpriseImproveData.legalPersonStartDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD HH:mm:ss" type="date" placeholder="开始时间" />
										</el-col>
										<el-col :span="2">
											<el-radio v-model="enterpriseImproveData.legalPersonPermanent" value="1" label="长期有效" @change="updatePermanent('legalPersonPermanent', 'legalPersonEndDate')" />
										</el-col>
										<el-col style="padding-right: 0" :span="11">
											<el-date-picker
												v-model="enterpriseImproveData.legalPersonEndDate"
												@change="updatePermanent('legalPersonEndDate', 'legalPersonPermanent', '0')"
												format="YYYY-MM-DD"
												value-format="YYYY-MM-DD HH:mm:ss"
												type="date"
												placeholder="结束时间"
											/>
										</el-col>
									</el-form-item>
								</el-col>
							</el-row>
						</el-col>
					</el-row>
				</template>
				<template #customDecadmin>
					<el-row :gutter="20" class="form-item-content">
						<el-col :span="6">
							<el-row>
								<el-col :span="24">
									<el-form-item label="证件类型：" prop="operatorIdType">
										<el-select placeholder="请选择证件类型" v-model="enterpriseImproveData.operatorIdType" disabled>
											<el-option value="DC01" label="身份证"></el-option>
										</el-select>
									</el-form-item>
								</el-col>
								<el-col :span="24">
									<el-form-item label="证件上传：" class="orgCode-class" prop="operatorCertificateFront">
										<UploadImg
											:id="enterpriseImproveData.operatorCertificateFront"
											:fileSize="10"
											width="160px"
											@change="(data:Object) => changeImage(data, 'operatorCertificateFront')"
											height="160px"
										/>
										<UploadImg
											:id="enterpriseImproveData.operatorCertificateBack"
											:fileSize="10"
											width="160px"
											@change="(data:Object) => changeImage(data, 'operatorCertificateBack')"
											height="160px"
										/>
									</el-form-item>
								</el-col>
							</el-row>
						</el-col>
						<el-col :span="18">
							<el-row :gutter="20">
								<el-col :span="12">
									<el-form-item label="管理员姓名：" prop="operatorName">
										<el-input v-model="enterpriseImproveData.operatorName" placeholder="请输入管理员姓名" />
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="管理员证件号码：" prop="operatorCertNo">
										<el-input v-model="enterpriseImproveData.operatorCertNo" placeholder="请输入法人证件号码" />
									</el-form-item>
								</el-col>
								<el-col :span="24">
									<el-form-item label="管理员证件有效期：" prop="operatorStartDate">
										<el-col style="padding-left: 0" :span="11">
											<el-date-picker v-model="enterpriseImproveData.operatorStartDate" format="YYYY-MM-DD" value-format="YYYY-MM-DD HH:mm:ss" type="date" placeholder="开始时间" />
										</el-col>
										<el-col :span="2">
											<el-radio v-model="enterpriseImproveData.operatorPermanent" value="1" label="长期有效" @change="updatePermanent('operatorPermanent', 'operatorEndDate')" />
										</el-col>
										<el-col style="padding-right: 0" :span="11">
											<el-date-picker
												v-model="enterpriseImproveData.operatorEndDate"
												@change="updatePermanent('operatorEndDate', 'operatorPermanent', '0')"
												format="YYYY-MM-DD"
												value-format="YYYY-MM-DD HH:mm:ss"
												type="date"
												placeholder="结束时间"
											/>
										</el-col>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="管理员手机号：" prop="operatorMobileNo">
										<el-input v-model="enterpriseImproveData.operatorMobileNo" placeholder="请输入法人手机号" disabled />
									</el-form-item>
								</el-col>
							</el-row>
						</el-col>
					</el-row>
				</template>
			</v-ele-detail>
		</el-form>
		<div class="submit">
			<el-button v-if="stepActive == 0" type="primary" class="w-[120px] h-[40px]" :loading="btnLoading" @click="onSubmit">下一步</el-button>
			<el-button v-if="stepActive == 41" type="primary" class="w-[120px] h-[40px]" :loading="btnLoading" @click="refreshSubmit">重新提交</el-button>
		</div>
	</div>
</template>
<script setup lang="ts">
import { useDicts } from '/@/hooks/useDicts';
import { getDictionary } from '/@/api/common/dictionary.js';
import { dictApi } from '/@/api/dict/index';
import { enterpriseFilingSubmit, resubmit } from '/@/api/enterprise-record/index';
import { ElMessage } from 'element-plus';
const props = defineProps({
	stepActive: {
		type: Number,
	},
	enterpriseInfoData: {
		type: Object,
		default: () => {},
	},
});
const btnLoading = ref<boolean>(false);
const useDict = useDicts('HlyCustomerNature', 'HlyEnterpriseScale', 'HlyIndustry');
// 企业类型
const corpTpList = computed(() => {
	return useDict['HlyCustomerNature'].value;
});
// 企业规模
const corpScaleList = computed(() => {
	return useDict['HlyEnterpriseScale'].value;
});
// 行业分类
const indClsList = computed(() => {
	return useDict['HlyIndustry'].value;
});
const whetherList = [
	{
		label: '是',
		value: '1',
	},
	{
		label: '否',
		value: '0',
	},
];
// 定义子组件向父组件传值/事件
const emit = defineEmits(['changeStep']);
const configData = {
	formData: {},
	list: [
		{
			title: '企业信息',
			type: 'customDec',
			disabled: true,
			isHiddenIcon: true,
			key: 'enterprise',
		},
		{
			title: '其他基本信息',
			type: 'customDec',
			disabled: true,
			isHiddenIcon: true,
			key: 'other',
		},
		{
			title: '法人信息',
			type: 'customDec',
			disabled: true,
			isHiddenIcon: true,
			key: 'legalPerson',
		},
		{
			title: '管理员信息',
			type: 'customDec',
			disabled: true,
			isHiddenIcon: true,
			key: 'admin',
		},
	],
};
interface EnterpriseImproveData {
	[key: string]: any;
}
const enterpriseImproveData = ref<EnterpriseImproveData>({
	enterpriseName: '',
	ecode: '',
	legalPersonName: '',
	businessLicense: '',
	corpTp: '',
	registerRegion: '',
	registerAddress: '',
	corpScale: '',
	termStartDate: '',
	businessScope: '',
	indCls: '',
	drAcg: '',
	isGreen: '',
	isTech: '',
	legalPersonIdType: '',
	legalCertificateFront: '',
	legalPersonIdCode: '',
	legalPersonPhone: '',
	legalPersonStartDate: '',
	operatorIdType: '',
	operatorCertificateFront: '',
	operatorName: '',
	operatorCertNo: '',
	operatorStartDate: '',
	operatorMobileNo: '',
});
const validatorDate = (rule: any, value: string, callback: any, longKey: string, startKey: string, endKey: string) => {
	if (enterpriseImproveData.value[longKey] == '0') {
		if (!enterpriseImproveData.value[startKey] || !enterpriseImproveData.value[endKey]) {
			callback(new Error('请选择日期'));
		} else if (new Date(enterpriseImproveData.value[startKey]) >= new Date(enterpriseImproveData.value[endKey])) {
			callback(new Error('开始日期不能大于结束日期'));
		} else {
			callback();
		}
	} else {
		if (!enterpriseImproveData.value[startKey]) {
			callback(new Error('请选择日期'));
		} else {
			callback();
		}
	}
};
const enterpriseImproveRules = ref({
	enterpriseName: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
	ecode: [{ required: true, message: '请输入统一社会信用代码', trigger: 'blur' }],
	legalPersonName: [{ required: true, message: '请输入法定代表人', trigger: 'blur' }],
	businessLicense: [{ required: true, message: '请上传营业执照', trigger: 'change' }],
	corpTp: [{ required: true, message: '请选择企业类型', trigger: 'change' }],
	registerRegion: [{ required: true, message: '请选择注册地区', trigger: 'change' }],
	registerAddress: [{ required: true, message: '请输入注册地址', trigger: 'blur' }],
	corpScale: [{ required: true, message: '请选择企业规模', trigger: 'change' }],
	termStartDate: [
		{ required: true, message: '请选择营业执照有效期', trigger: ['change', 'blur'] },
		{ validator: (rule: any, value: string, callback: any) => validatorDate(rule, value, callback, 'termPermanent', 'termStartDate', 'termEndDate'), trigger: 'change' },
	],
	businessScope: [
		{ required: true, message: '请输入经营范围', trigger: 'blur' },
		{ max: 300, message: '最多可输入300字符' },
	],
	indCls: [{ required: true, message: '请选择行业分类', trigger: 'change' }],
	drAcg: [{ required: true, message: '请选择是否涉农', trigger: 'change' }],
	isGreen: [{ required: true, message: '请选择是否绿色', trigger: 'change' }],
	isTech: [{ required: true, message: '请选择是否科技', trigger: 'change' }],
	legalPersonIdType: [{ required: true, message: '请选择证件类型', trigger: 'change' }],
	legalCertificateFront: [{ required: true, message: '请上传证件上传', trigger: 'change' }],
	legalPersonIdCode: [{ required: true, message: '请输入法人证件号码', trigger: 'blur' }],
	legalPersonPhone: [{ required: true, message: '请输入法人手机号', trigger: 'blur' }],
	legalPersonStartDate: [
		{ required: true, message: '请选择法人证件有效期', trigger: ['change', 'blur'] },
		{ validator: (rule: any, value: string, callback: any) => validatorDate(rule, value, callback, 'legalPersonPermanent', 'legalPersonStartDate', 'legalPersonEndDate'), trigger: 'change' },
	],
	operatorIdType: [{ required: true, message: '请选择证件类型', trigger: 'change' }],
	operatorCertificateFront: [{ required: true, message: '请上传证件', trigger: 'change' }],
	operatorName: [{ required: true, message: '请输入管理员姓名', trigger: 'blur' }],
	operatorCertNo: [{ required: true, message: '请输入法人证件号码', trigger: 'blur' }],
	operatorStartDate: [
		{ required: true, message: '请选择管理员证件有效期', trigger: ['change', 'blur'] },
		{ validator: (rule: any, value: string, callback: any) => validatorDate(rule, value, callback, 'operatorPermanent', 'operatorStartDate', 'operatorEndDate'), trigger: 'change' },
	],
	operatorMobileNo: [{ required: true, message: '请输入法人手机号', trigger: 'blur' }],
});
// 下一步
const enterpriseImproveFromRef = ref();
const onSubmit = () => {
	enterpriseImproveFromRef.value.validate((valid: boolean) => {
		if (valid) {
			btnLoading.value = true;
			let params = Object.assign({}, enterpriseImproveData.value);
			params.termEndDate = params.termPermanent == '1' ? '9999-12-31 00:00:00' : params.termEndDate;
			params.legalPersonEndDate = params.legalPersonPermanent == '1' ? '9999-12-31 00:00:00' : params.legalPersonEndDate;
			params.operatorEndDate = params.operatorPermanent == '1' ? '9999-12-31 00:00:00' : params.operatorEndDate;
			params.registerRegion = params.registerRegion?.join(',');
			let fileIds = {
				businessLicense: params.businessLicense,
				legalCertificateFront: params.legalCertificateFront,
				legalCertificateBack: params.legalCertificateBack,
			};
			let operatorFileIds = {
				legalCertificateFront: params.operatorCertificateFront,
				legalCertificateBack: params.operatorCertificateBack,
			};
			params.fileIds = JSON.stringify(fileIds);
			params.operatorFileIds = JSON.stringify(operatorFileIds);
			enterpriseFilingSubmit(params)
				.then((res) => {
					if (res.code == 200) {
						ElMessage.success(res.msg);
						emit('changeStep', 1);
					} else {
						ElMessage.error(res.msg);
					}
				})
				.finally(() => {
					btnLoading.value = false;
				});
		}
	});
};

// 重新提交
const refreshSubmit = () => {
	enterpriseImproveFromRef.value.validate((valid: boolean) => {
		if (valid) {
			btnLoading.value = true;
			let params = Object.assign({}, enterpriseImproveData.value);
			params.termEndDate = params.termPermanent == '1' ? '9999-12-31 00:00:00' : params.termEndDate;
			params.legalPersonEndDate = params.legalPersonPermanent == '1' ? '9999-12-31 00:00:00' : params.legalPersonEndDate;
			params.operatorEndDate = params.operatorPermanent == '1' ? '9999-12-31 00:00:00' : params.operatorEndDate;
			params.registerRegion = params.registerRegion.join(',');
			let fileIds = {
				businessLicense: params.businessLicense,
				legalCertificateFront: params.legalCertificateFront,
				legalCertificateBack: params.legalCertificateBack,
			};
			let operatorFileIds = {
				legalCertificateFront: params.operatorCertificateFront,
				legalCertificateBack: params.operatorCertificateBack,
			};
			params.fileIds = JSON.stringify(fileIds);
			params.operatorFileIds = JSON.stringify(operatorFileIds);
			resubmit(params)
				.then((res) => {
					if (res.code == 200) {
						ElMessage.success(res.msg);
						emit('changeStep', -1);
					} else {
						ElMessage.error(res.msg);
					}
				})
				.finally(() => {
					btnLoading.value = false;
				});
		}
	});
};
const updatePermanent = (changeKey: string, key: string, val?: string) => {
	if (enterpriseImproveData.value[changeKey]) {
		enterpriseImproveData.value[key] = val || '';
	}
};
// 获取图片id
const changeImage = (data: Object, key: string) => {
	enterpriseImproveData.value[key] = data.id;
};
// 改变地区
const officeAddress = ref();
// 地区字典
const areaDataNoHasArea = ref();
const onClickOfficeAreaChange = () => {
	// officeAddress.value.getCheckedNodes()[0];
};
watch(
	() => props.enterpriseInfoData,
	(newEnterpriseInfoData) => {
		enterpriseImproveData.value = JSON.parse(JSON.stringify(newEnterpriseInfoData));
	}
);
const init = () => {
	// 获取地区(特殊字典因此没用公用方法)
	getDictionary(dictApi.allArea).then((res: any) => {
		areaDataNoHasArea.value = res;
	});
};
init();
</script>
<style lang="scss" scoped>
.enterprise-improve {
	width: 100%;
	height: 100%;
	overflow-y: scroll;
}
::v-deep.el-form {
	.el-form-item {
		width: 100%;
	}
	.el-form-item__content {
		// width: calc(100% - 160px);
		width: 100%;

		.el-input,
		.el-select {
			width: 100%;
		}
	}
	.orgCode-class .el-form-item__content {
		display: flex;
		justify-content: space-between;
	}
	.license-class .el-form-item__content {
		display: flex;
		justify-content: center;
	}
}
.submit {
	width: 100%;
	display: flex;
	justify-content: center;
	padding: 15px 0;
	margin-top: 10px;
	background: #fff;
}
</style>
