<template>
	<div class="enterprise-authentication">
		<div class="authentication-step">
			<el-steps style="width: 800px" :active="active">
				<el-step v-for="(item, index) in stepsList" :key="index">
					<template #icon>
						<span class="step-item">
							<span class="step-item-success" v-if="active > index + 1"><CircleCheck class="w-[30px] h-[30px]" /></span>
							<span class="step-item-active" v-else-if="active === index + 1">{{ index + 1 }}</span>
							<span class="step-item-wait" v-else-if="active < index + 1">{{ index + 1 }}</span>
							<span class="text-[16px] mx-[12px]" v-if="active > index + 1">{{ item.title }}</span>
							<span class="text-[16px] mx-[12px]" v-if="active === index + 1">{{ item.title }}</span>
							<span class="text-[16px] mx-[12px] text-[#909399]" v-if="active < index + 1">{{ item.title }}</span>
						</span>
					</template>
				</el-step>
			</el-steps>
		</div>
		<div class="authentication-content">
			<!-- 个人认证 -->
			<div class="person" v-if="props.stepActive == 1 && active == 1">
				<h3>管理员用户</h3>
				<div class="user-card">
					<div class="text-center" v-if="status == 1">
						<p>该用户尚未完成个人实名认证授权。请选择以下方式完成个人实名认证并授权。</p>
						<p>完成授权后平台将获取该用户的个人信息及用章权限，并在后续的票据业务中代表该用户发起合同签署;</p>
					</div>
					<div v-if="status == 2">
						<p>该用户已完成个人实名认证尚未完成个人授权。请点击发起授权按纽完成个人授权；</p>
						<p>完成授权后平台将获取该用户的个人信息及用章权限，并在后续的票据业务中代表该用户发起合同签署；</p>
						<p>点击发送授权按钮后，系统将自动发起授权绑定或发送授权短信至用户手机号。请根据系统提示，完成后续授权操作。</p>
					</div>
					<div class="text-center" v-if="status == 3">
						<p>恭喜您已完成认证及授权！请先阅读并同意以下协议后继续操作。</p>
					</div>
					<el-form label-width="100px">
						<el-form-item label="认证方式：" v-if="status == 1">
							<el-button type="primary">手机号验证</el-button>
						</el-form-item>
						<el-form-item label="姓名：">
							<el-input v-model="personAuthData.name" disabled />
						</el-form-item>
						<el-form-item label="身份证号：">
							<el-input v-model="personAuthData.idCard" disabled />
						</el-form-item>
						<el-form-item label="手机号：">
							<el-input v-model="personAuthData.phone" disabled />
						</el-form-item>
						<el-form-item label="验证码：" v-if="status == 1">
							<verificationCode v-model="personAuthData.code" :verifyCallback="personAuthVerifyCallback" @callback="personAuthCallback" />
						</el-form-item>
						<el-form-item v-if="status == 3" class="agreement-class">
							<el-checkbox class="mr-[0]" v-model="isRead" />
							<span class="ml-[5px]">您已阅读并同意</span>
							<span class="agreement-link" @click="lookAgreement('eSignServiceAgreement', '服务协议')">《服务协议》</span>
							<span class="agreement-link" @click="lookAgreement('PrivacyPolicy', '隐私政策')">《隐私政策》</span>
							<span class="agreement-link" @click="lookAgreement('UseAgreement', '数字证书申请及使用协议')">《数字证书申请及使用协议》</span>
						</el-form-item>
						<el-form-item class="person-btn">
							<el-button type="primary" class="w-[120px] h-[40px]" :loading="btnLoading" v-if="status == 2 && authStatus == 1" @click="sendAuthorize">发起授权</el-button>
							<el-button type="primary" class="w-[120px] h-[40px]" :loading="btnLoading" v-if="status == 2 && authStatus == 2" @click="upDateRerult">结果更新并继续</el-button>
							<el-button type="primary" class="w-[120px] h-[40px]" :loading="btnLoading" v-if="status == 1" @click="sendAuth">发起认证并授权</el-button>
							<el-button type="primary" class="w-[120px] h-[40px]" v-if="status == 3" @click="continueStep">继 续</el-button>
							<el-button type="primary" class="w-[120px] h-[40px]" v-if="props.stepActive == 1" @click="previousStep">上一步</el-button>
						</el-form-item>
					</el-form>
				</div>
			</div>
			<!-- 企业认证 -->
			<div class="enterprise" v-if="props.stepActive == 10 && active == 2">
				<h3>企业信息</h3>
				<el-row :gutter="20" align="middle" class="inform-class">
					<el-col :span="12">
						<span class="text-[14px] text-[#86909C]">企业名称：</span>
						<span class="text-[14px] text-[#1D2129]">{{ enterpriseAuthData.enterpriseName }}</span>
					</el-col>
					<el-col :span="12">
						<span class="text-[14px] text-[#86909C]">统一社会信用代码：</span>
						<span class="text-[14px] text-[#1D2129]">{{ enterpriseAuthData.ecode }}</span>
					</el-col>
					<el-col :span="12">
						<span class="text-[14px] text-[#86909C]">法定代表人：</span>
						<span class="text-[14px] text-[#1D2129]">{{ enterpriseAuthData.legalPersonName }}</span>
					</el-col>
					<el-col :span="12">
						<span class="text-[14px] text-[#86909C]">企业营业执照：</span>
						<el-image style="width: 100px" :preview-src-list="[enterpriseAuthData.businessLicenseUrl]" :src="enterpriseAuthData.businessLicenseUrl" />
					</el-col>
				</el-row>
				<h3>认证授权</h3>
				<div class="user-card">
					<div v-if="enterpriseStatus == 2 && enterpriseAuthStatus == 1">
						<p>贵企业已完成企业实名认证尚未授权。为保障后续票据业务的开展，请点击发起授权按钮完成企业授权;</p>
						<p>完成授权后平台将获取该企业/组织的用印、组织成员资源的管理权限和基本信息，并在后续的票据业务中代表企业用户发起合同签署;</p>
						<p>现请您点击发起企业授权按钮，发起授权后将向贵司管理员手机发送授权短信。</p>
					</div>
					<div v-if="enterpriseStatus == 2 && enterpriseAuthStatus == 2">
						<p>贵企业已完成企业认证尚未授权。</p>
						<p>完成授权后平台将获取该企业/组织的用印、组织成员资源的管理权限和基本信息，并在后续的票据业务中代表企业用户发起合同签署;</p>
						<p>平台已向该用户手机发送授权短信，请及时查看短信并在手机上完成授权后，返回系统点击结果更新并继续按钮。</p>
					</div>
					<div v-if="enterpriseStatus == 1">
						<p>贵企业未完成企业实名认证授权。请选择以下方式完成企业认证并授权。</p>
						<p>完成授权后平台将获取该企业/组织的用印、组织成员资源的管理权限和基本信息，并在后续的票据业务中代表企业用户发起合同签署</p>
					</div>
					<el-form ref="enterpriseFormRef" label-width="130px" :model="enterpriseAuthData" :rules="enterpriseAuthRules">
						<el-form-item label="认证方式：" v-if="enterpriseStatus == 1">
							<el-button type="primary">企业法人手机号认证</el-button>
						</el-form-item>
						<el-form-item label="企业名称：" prop="enterpriseName" v-if="enterpriseStatus == 2 || enterpriseStatus == 3">
							<el-input v-model="enterpriseAuthData.enterpriseName" disabled />
						</el-form-item>
						<el-form-item label="企业管理员：" v-if="enterpriseStatus == 2" prop="adminUserId">
							<el-select v-model="enterpriseAuthData.adminUserId" :disabled="enterpriseAuthStatus == 2" @change="changeUser">
								<el-option v-for="item in enterpriseAuthData.users" :key="item.userId" :value="item.userId" :label="item.realName"></el-option>
							</el-select>
						</el-form-item>
						<el-form-item label="管理员手机号：" prop="legalPersonPhone" v-if="enterpriseStatus == 2">
							<el-input v-model="enterpriseAuthData.legalPersonPhone" disabled />
						</el-form-item>
						<el-form-item label="法人代表姓名：" prop="legalPersonName" v-if="enterpriseStatus == 1">
							<el-input v-model="enterpriseAuthData.legalPersonName" disabled />
						</el-form-item>
						<el-form-item label="法人身份证号：" prop="legalPersonIdCard" v-if="enterpriseStatus == 1">
							<el-input v-model="enterpriseAuthData.legalPersonIdCard" disabled />
						</el-form-item>
						<el-form-item label="法人手机号：" prop="legalPersonPhone" v-if="enterpriseStatus == 1">
							<el-input placeholder="请输入法人手机号" v-model="enterpriseAuthData.legalPersonPhone" />
						</el-form-item>
						<el-form-item label="验证码：" prop="code" v-if="enterpriseStatus == 1">
							<verificationCode v-model="enterpriseAuthData.code" :verifyCallback="enterpriseVerifyCallback" @callback="enterpriseAuthCallback" />
						</el-form-item>
						<el-form-item v-if="enterpriseStatus == 3" class="agreement-class">
							<el-checkbox class="mr-[0]" v-model="enterpriseIsRead" />
							<span class="ml-[5px]">您已阅读并同意</span>
							<span class="agreement-link" @click="lookAgreement('eSignServiceAgreement', '服务协议')">《服务协议》</span>
							<span class="agreement-link" @click="lookAgreement('PrivacyPolicy', '隐私政策')">《隐私政策》</span>
							<span class="agreement-link" @click="lookAgreement('UseAgreement', '数字证书申请及使用协议')">《数字证书申请及使用协议》</span>
						</el-form-item>
						<el-form-item class="person-btn">
							<el-button type="primary" class="w-[120px] h-[40px]" :loading="btnLoading" @click="sendAuthorizeEnterprise" v-if="enterpriseStatus == 2 && enterpriseAuthStatus == 1">发起授权</el-button>
							<el-button type="primary" class="w-[120px] h-[40px]" :loading="btnLoading" @click="upDateRerultEnterprise" v-if="enterpriseStatus == 2 && enterpriseAuthStatus == 2"
								>结果更新并继续</el-button
							>
							<el-button type="primary" class="w-[120px] h-[40px]" :loading="btnLoading" @click="sendAuthEnterprise" v-if="enterpriseStatus == 1">发起认证并授权</el-button>
							<el-button type="primary" class="w-[120px] h-[40px]" @click="continueStepEnterprise" v-if="enterpriseStatus == 3">继 续</el-button>
						</el-form-item>
					</el-form>
				</div>
			</div>
			<!-- 完成 -->
			<finishCom :item="getSuccessData()" v-if="(props.stepActive == 10 && active == 1) || (props.stepActive == 11 && active == 3)" @nextStep="nextStep" />
			<co-preview v-if="previewUrl" v-model="previewUrl" width="60%" :title="previewTitle" layer />
		</div>
	</div>
</template>
<script setup lang="ts">
import {
	personSignStatus,
	createUser,
	personRealNameAndAuth,
	personAuth,
	signAgree,
	enterpriseSignStatus,
	enterpriseRealNameAndAuth,
	enterpriseAuth,
	legalPersonVerify,
	fallback,
} from '/@/api/enterprise-record/index';
import { getTemplateFile } from '/@/api/common/constants.js';
import { useUserInfo } from '/@/stores/userInfo';
import { ElMessageBox, ElMessage } from 'element-plus';
import { CircleCheck } from '@element-plus/icons-vue';
import { encryptedData } from '/@/utils/encrypt.js';
import { getFiles } from '/@/api/common/upload.js';
import { rule } from '/@/utils/validate';
const userInfos = useUserInfo();
const verificationCode = defineAsyncComponent(() => import('/@/components/verification-code/index.vue'));
const finishCom = defineAsyncComponent(() => import('./finish.vue'));
const props = defineProps({
	stepActive: {
		type: Number,
	},
});
const btnLoading = ref(false);
const previewUrl = ref<string>('');
const previewTitle = ref<string>('预览');
// 预览
const lookAgreement = (key: string, title: string) => {
	getTemplateFile(key).then((res) => {
		previewUrl.value = res.data[0].fileUrl;
	});
	previewTitle.value = title;
};
const active = ref(1);
const stepsList = [
	{
		title: '个人实名认证授权',
	},
	{
		title: '企业人实名认证授权',
	},
	{
		title: '认证授权完成',
	},
];
const getSuccessData = () => {
	if (props.stepActive == 10) {
		return {
			title: '个人实名认证',
			img: '',
			dec: '个人认证授权完成！',
		};
	} else if (props.stepActive == 11) {
		return {
			title: '企业已认证',
			img: '',
			dec: '您已完成企业授权！',
		};
	}
	return {};
};
// 上一步
const previousStep = () => {
	fallback({ ecode: userInfos.userInfos.ecode }).then((res) => {
		if (res.code == 200) {
			emit('changeStep', 0);
		} else {
			ElMessage.error(res.msg);
		}
	});
};
// 确定并继续
const nextStep = () => {
	if (props.stepActive == 10) {
		status.value = 1;
		active.value = 2;
	} else if (props.stepActive == 11) {
		emit('changeStep', 12);
	}
};
// 个人认证
const status = ref(1);
const authStatus = ref(1);
const isRead = ref(false);
const encryptedDataPhone = ref<string>('');
const personAuthData = ref({
	serialNumber: '', //短信验证码唯⼀识
	code: '', //短信验证码
	zaeqUserId: '', //创建用户智安e签后返回的用户id
	phone: '', //手机号
	name: '', //真实姓名
	idCard: '', //身份证号
});
// 发起授权
const sendAuthorize = () => {
	personAuthData.value.ecode = userInfos.userInfos.ecode;
	btnLoading.value = true;
	personAuth(personAuthData.value)
		.then((res) => {
			if (res.code == 200) {
				ElMessage.success(res.msg);
				let msg = res.data
					? '平台已向该用户手机发送授权短信，请及时查看短信并在手机上完成授权后，返回系统点击结果更新并继续按钮。'
					: '用户授权中，请等待授权结果。如长时间仍未更新，请点击结果更新并继续按钮。';
				ElMessageBox.confirm(msg, {
					title: '系统提示',
					showClose: true,
					closeOnClickModal: false,
					closeOnPressEscape: false,
					showConfirmButton: false,
					showCancelButton: false,
				}).catch(() => {
					authStatus.value = 2;
				});
			} else {
				ElMessage.error(res.msg);
			}
		})
		.finally(() => {
			btnLoading.value = false;
		});
};
// 结果更新并继续
const upDateRerult = () => {
	// 获取个人认证信息
	getPersonSignStatusData();
};
// 发起认证并授权
const sendAuth = () => {
	if (!personAuthData.value.serialNumber || !personAuthData.value.zaeqUserId) {
		ElMessage.warning('请先获取验证码');
	}
	btnLoading.value = true;
	personAuthData.value.ecode = userInfos.userInfos.ecode;
	personRealNameAndAuth(personAuthData.value)
		.then((res) => {
			if (res.code == 200) {
				ElMessage.success(res.msg);
				status.value = 3;
			} else {
				ElMessage.error(res.msg);
			}
		})
		.finally(() => {
			btnLoading.value = false;
		});
};

// 继续
// 定义子组件向父组件传值/事件
const emit = defineEmits(['changeStep']);
const continueStep = () => {
	if (!isRead.value) {
		ElMessage.warning('请阅读协议');
		return;
	}
	signAgree({ ifAgreeUser: 1 }).then((res) => {
		if (res.code == 200) {
			ElMessage.success(res.msg);
			emit('changeStep', 10);
		} else {
			ElMessage.error(res.msg);
		}
	});
};
const personAuthVerifyCallback = () => {
	if (!personAuthData.value.phone) {
		ElMessage.error('请输入手机号');
		return false;
	}
	return true;
};
// 验证码回调
const personAuthCallback = (type: string, beginCountdownCallBack: any) => {
	if (type === 'verifySuccess') {
		const _params = {
			validateRealNameType: '0',
			phone: personAuthData.value.phone,
			realName: personAuthData.value.name,
			idCard: personAuthData.value.idCard,
			roles: 0,
			type: '1',
			socialCode: userInfos.userInfos.ecode,
		};
		createUser(_params)
			.then((res) => {
				if (res.code == 200) {
					personAuthData.value.serialNumber = res.data.serialNumber;
					personAuthData.value.zaeqUserId = res.data.userId;
					beginCountdownCallBack();
				} else {
					ElMessage.error(res.msg);
				}
			})
			.catch((error) => {
				beginCountdownCallBack(error);
			});
	}
};
// -----------------------------

// 企业认证
const enterpriseFormRef = ref();
const enterpriseStatus = ref(1);
const enterpriseAuthStatus = ref(1);
const enterpriseIsRead = ref(false);
interface EnterpriseAuthData {
	[key: string]: any;
}
const enterpriseAuthData = ref<EnterpriseAuthData>({
	businessLicense: '',
	ecode: '',
	enterpriseName: '',
	legalPersonIdCard: '',
	legalPersonName: '',
	legalPersonPhone: '',
	zaeqEnterpriseId: '',
	zaeqUserId: '',
	adm1nUserId: '',
	businessLicenseUrl: '',
	code: '',
});
const enterpriseAuthRules = ref({
	enterpriseName: [{ required: true, message: '请输入企业名称', trigger: 'blur' }],
	legalPersonIdCard: [{ required: true, message: '请输入法人身份证号', trigger: 'blur' }],
	legalPersonName: [{ required: true, message: '请输入法人名称', trigger: 'blur' }],
	legalPersonPhone: [{ required: true, validator: rule.validatePhone, trigger: 'blur' }],
	adminUserId: [{ required: true, message: '请选择企业管理员', trigger: 'change' }],
	code: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
});
const changeUser = (val: string) => {
	enterpriseAuthData.value.legalPersonPhone = enterpriseAuthData.value.users.filter((item) => item.userId == val)[0].phone;
};
// 发起授权
const sendAuthorizeEnterprise = () => {
	btnLoading.value = true;
	enterpriseAuthData.value.ecode = userInfos.userInfos.ecode;
	enterpriseAuth(enterpriseAuthData.value)
		.then((res) => {
			if (res.code == 200) {
				ElMessage.success(res.msg);
				enterpriseAuthStatus.value = 2;
			} else {
				ElMessage.error(res.msg);
			}
		})
		.finally(() => {
			btnLoading.value = false;
		});
};
// 结果更新并继续
const upDateRerultEnterprise = () => {
	// 获取企业认证信息
	getenterpriseSignStatusData('update');
};
// 发起认证并授权
const sendAuthEnterprise = () => {
	if (!enterpriseAuthData.value.serialNumber || !enterpriseAuthData.value.zaeqEnterpriseId) {
		ElMessage.warning('请先获取验证码');
	}
	btnLoading.value = true;
	enterpriseAuthData.value.ecode = userInfos.userInfos.ecode;
	enterpriseRealNameAndAuth(enterpriseAuthData.value)
		.then((res) => {
			if (res.code == 200) {
				ElMessage.success(res.msg);
				enterpriseStatus.value = 3;
			} else {
				ElMessage.error(res.msg);
			}
		})
		.finally(() => {
			btnLoading.value = false;
		});
};
// 继续
// 定义子组件向父组件传值/事件
const continueStepEnterprise = () => {
	if (!enterpriseIsRead.value) {
		ElMessage.warning('请阅读协议');
		return;
	}
	signAgree({ ifAgreeEnterprise: 1 }).then((res) => {
		if (res.code == 200) {
			ElMessage.success(res.msg);
			active.value = 3;
			emit('changeStep', 11);
		} else {
			ElMessage.error(res.msg);
		}
	});
};
// 企业手机号校验
const enterpriseVerifyCallback = () => {
	return enterpriseFormRef.value.validateField('legalPersonPhone', (vaild: boolean) => {
		return vaild;
	});
};
// 企业验证码回调
const enterpriseAuthCallback = (type: string, beginCountdownCallBack: any) => {
	if (type === 'verifySuccess') {
		const _params = {
			enterpriseName: enterpriseAuthData.value.enterpriseName,
			phone: enterpriseAuthData.value.legalPersonPhone,
			legalPerson: enterpriseAuthData.value.legalPersonName,
			userId: enterpriseAuthData.value.zaeqUserId,
			socialCode: userInfos.userInfos.ecode,
		};
		legalPersonVerify(_params)
			.then((res) => {
				if (res.code == 200) {
					enterpriseAuthData.value.serialNumber = res.data.serialNumber;
					enterpriseAuthData.value.zaeqEnterpriseId = res.data.enterpriseId;
					beginCountdownCallBack();
				} else {
					ElMessage.error(res.msg);
				}
			})
			.catch((error) => {
				beginCountdownCallBack(error);
			});
	}
};
// ----------------

const init = () => {
	switch (props.stepActive) {
		case 1:
			active.value = 1;
			break;
		case 10:
			active.value = 2;
			break;
		case 11:
			active.value = 3;
			break;
	}
	// 获取个人认证信息
	getPersonSignStatusData();
	// 获取企业认证信息
	getenterpriseSignStatusData();
};
// 获取个人认证信息
const getPersonSignStatusData = () => {
	btnLoading.value = true;
	personSignStatus({ ecode: userInfos.userInfos.ecode })
		.then((res) => {
			if (res.code == 200) {
				status.value = res.data.type;
				encryptedDataPhone.value = encryptedData(res.data.phone);
				personAuthData.value = res.data;
			} else {
				ElMessage.error(res.msg);
			}
		})
		.finally(() => {
			btnLoading.value = false;
		});
};
// 获取企业认证信息
const getenterpriseSignStatusData = (type?: string) => {
	btnLoading.value = true;
	enterpriseSignStatus({ ecode: userInfos.userInfos.ecode })
		.then((res) => {
			if (res.code == 200) {
				enterpriseStatus.value = res.data.type;
				if (res.data.businessLicense) {
					getFileUrl([res.data.businessLicense]);
				}
				if (type == 'update') {
					enterpriseAuthData.value.type = res.data.type;
				} else {
					enterpriseAuthData.value = res.data;
				}
			} else {
				ElMessage.error(res.msg);
			}
		})
		.finally(() => {
			btnLoading.value = false;
		});
};
// 获取文件地址
const getFileUrl = (ids: string[]) => {
	getFiles(ids).then((res) => {
		if (res.data && res.data.length) {
			enterpriseAuthData.value.businessLicenseUrl = res.data[0].fileUrl;
		} else {
			ElMessage.error('没有查询到附件');
		}
	});
};
onMounted(() => {
	init();
});
</script>
<style lang="scss" scoped>
.enterprise-authentication {
	width: 100%;
	height: 100%;
	overflow: hidden;
}
.authentication-content {
	width: 100%;
	height: calc(100% - 100px - 20px);
	margin-top: 20px;
	background: #fff;
	border-radius: 4px;
	overflow-y: scroll;
	display: flex;
	justify-content: center;
	padding: 20px;
	.person,
	.enterprise {
		width: 800px;
		h3 {
			margin-bottom: 20px;
			font-size: 18px;
			width: 100%;
			display: flex;
			align-items: center;
			font-weight: bold;
			&:before {
				content: '';
				display: inline-block;
				width: 4px;
				height: 20px;
				margin-right: 10px;
				margin-top: -2px;
				background: #165dff;
			}
		}
		.user-card {
			padding: 0 30px;
			p {
				color: var(--el-color-primary);
				// text-align: center;
			}
			:deep(.el-form) {
				width: 500px;
				margin-left: 100px;
				margin-top: 20px;
				.person-btn .el-form-item__content {
					display: flex;
					justify-content: center;
					margin-left: 0 !important;
				}
				.agreement-class .el-form-item__content {
					margin-left: 0 !important;
					display: flex;
					justify-content: center;
					.agreement-link {
						color: var(--el-color-primary);
						cursor: pointer;
					}
				}
			}
		}
		.inform-class {
			.el-col {
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
	// .enterprise {
	// 	width: 800px;
	// }
}
.authentication-step {
	width: 100%;
	height: 100px;
	background: #fff;
	border-radius: 4px;
	display: flex;
	justify-content: center;
	align-items: center;
	:deep(.el-steps) {
		.step-item {
			display: flex;
			flex-direction: column;
			align-items: center;
		}
		.el-step__icon {
			width: 40px;
			height: 40px;
		}
		.el-step.is-horizontal .el-step__line {
			top: 8px;
		}
		.step-item-success {
			width: 30px;
			height: 30px;
			// border: 1px solid var(--el-color-primary);
			border-radius: 30px;
			display: flex;
			justify-content: center;
			align-items: center;
		}
		.step-item-active {
			width: 30px;
			height: 30px;
			font-size: 16px;
			background: var(--el-color-primary);
			border: 1px solid var(--el-color-primary);
			color: #fff;
			border-radius: 30px;
			display: flex;
			justify-content: center;
			align-items: center;
		}
		.step-item-wait {
			width: 30px;
			height: 30px;
			font-size: 16px;
			border: 1px solid var(--el-color-info);
			border-radius: 30px;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
}
</style>
