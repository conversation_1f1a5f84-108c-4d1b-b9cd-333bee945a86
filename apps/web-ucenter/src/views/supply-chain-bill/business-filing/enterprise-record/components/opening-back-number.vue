<template>
	<el-dialog v-model="dialogTableVisible" title="选择开户行" :close-on-click-modal="false" width="1000">
		<co-search :config="datas.searchConfig" :dic="dicData" @search="onSearchHandle" />
		<co-table single-mode :config="tableConfig" :header="datas.openingBankTableHeader" @dicLoaded="onDicLoaded" @loaded="dsTableLoaded">
			<template #radio="{ row }">
				<el-radio v-model="selectionId" :value="row.orgNo" @change="selectionChange(row)" />
			</template>
		</co-table>
		<template #footer>
			<div class="dialog-footer">
				<el-button type="primary" @click="handleSubmit">确定</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import datas from './index.data';
import { dictApi } from '/@/api/dict/index';
import { getListOrgPage } from '/@/api/supply-chain-bill/bank-account-manage';

interface SearchParams {
	orgNo?: string;
	ubankNo?: string;
	ubankName?: string;
}

const tableConfig = {
	request: {
		apiName: getListOrgPage,
	},
	operation: false,
	dic: {
		orgStatus: dictApi['orgStatus'], // 机构状态
	},
};

const selectionId = ref('');
const bankData = ref({});
const selectionChange = (row: any) => {
	bankData.value = row;
};

const dialogTableVisible = ref<boolean>(false);
const openDialog = () => {
	dialogTableVisible.value = true;
};

const dicData = ref({});
// 字典加载完毕
const onDicLoaded = (data: any) => {
	dicData.value = data;
};

let onSearch: any = null;
// 搜索
const onSearchHandle = (data: SearchParams) => {
	onSearch({ params: data });
};
const dsTableLoaded = ({ getDataList }: any) => {
	onSearch = getDataList;
};

const emit = defineEmits(['callback']);
const handleSubmit = () => {
	emit('callback', bankData.value);
	dialogTableVisible.value = false;
};

defineExpose({
	openDialog,
});
</script>

<style scoped></style>
