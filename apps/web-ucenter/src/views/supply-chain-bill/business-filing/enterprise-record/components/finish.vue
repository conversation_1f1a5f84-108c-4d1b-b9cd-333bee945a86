<template>
	<div class="finish">
		<h3 class="text-[28px] font-bold" v-if="props.item.title">{{ props.item.title }}</h3>
		<el-image class="my-[20px]" :src="props.item.img ? props.item.img : image" />
		<span class="text-[16px]" v-if="props.item.dec">{{ props.item.dec }}</span>
		<el-button v-if="props.isShowButton" type="primary" class="mt-[40px] w-[120px] h-[40px]" @click="confirm">确定并继续</el-button>
		<slot class="mt-[40px]" name="button"></slot>
	</div>
</template>
<script lang="ts" setup>
import image from '/@/assets/success.png';
const props = defineProps({
	item: {
		type: Object,
		default: () => {},
	},
	isShowButton: {
		type: Boolean,
		default: true,
	},
});
const emit = defineEmits(['nextStep']);
// 确定并继续
const confirm = () => {
	emit('nextStep');
};
</script>
<style lang="scss" scoped>
.finish {
	width: 400px;
	height: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
</style>
