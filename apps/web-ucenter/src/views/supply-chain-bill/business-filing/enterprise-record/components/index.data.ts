export default {
	settleTableHeader: [
		{ prop: 'bankCardNumber', label: '对公账号', width: 300, showOverflowTooltip: true },
		{ prop: 'bankBranch', label: '开户银行支行', width: 400, showOverflowTooltip: true },
		{ prop: 'accountName', label: '账户名称', width: 300, showOverflowTooltip: true },
		{ prop: 'openingBankNumber', label: '开户行行号', width: 280, showOverflowTooltip: true },
		{ prop: 'backupClearingAccount', label: '备用清算账户', showOverflowTooltip: true },
	],
	tableHeader: [
		{ prop: 'insertTime', label: '提交时间', width: 160, showOverflowTooltip: true },
		{ prop: 'bankHeadOffice', label: '开户银行总行', width: 200, showOverflowTooltip: true },
		{ prop: 'bankBranch', label: '开户银行支行', width: 300, showOverflowTooltip: true },
		{ prop: 'openingBankNumber', label: '开户行行号', width: 200, showOverflowTooltip: true },
		{ prop: 'bankCardNumber', label: '对公账号', width: 200, showOverflowTooltip: true },
		{ prop: 'openingProvince', label: '省份', width: 200, showOverflowTooltip: true },
		{ prop: 'openingCity', label: '地区', width: 200, showOverflowTooltip: true },
		{ prop: 'accountStatus', label: '账户验证状态', fixed: 'right', width: 150, showOverflowTooltip: true },
		{ prop: 'accountRegistrationStatus', label: '账户登记状态', fixed: 'right', width: 120, showOverflowTooltip: true },
		{ prop: 'backupClearingAccount', label: '是否备用', fixed: 'right', width: 100 },
		{ prop: 'failureReason', label: '失败原因', fixed: 'right', width: 200, showOverflowTooltip: true },
	],
	searchConfig: {
		items: [
			{
				prop: 'orgNo',
				type: 'input',
				attrs: {
					placeholder: '机构参与者代码',
					clearable: true,
				},
			},
			{
				prop: 'ubankNo',
				type: 'input',
				attrs: {
					placeholder: '开户行行号',
					clearable: true,
				},
			},
			{
				prop: 'ubankName',
				type: 'input',
				attrs: {
					placeholder: '机构参与者名称',
					clearable: true,
				},
			},
		],
	},
	openingBankTableHeader: [
		{ prop: 'radio', width: 50 },
		{ prop: 'orgNo', label: '机构参与者代码', width: 180, showOverflowTooltip: true },
		{ prop: 'parentUbankNo', label: '开户行行号', showOverflowTooltip: true },
		{ prop: 'ubankName', label: '机构参与者名称', width: 350, showOverflowTooltip: true },
		{ prop: 'orgStatus', label: '状态', width: 100, showOverflowTooltip: true },
	],
	rules: {
		openingBankNumber: [{ required: true, message: '请输入开户行行号', trigger: 'change' }],
		bankBranch: [{ required: true, message: '请输入开户行支行', trigger: 'change' }],
		bankHeadOffice: [{ required: true, message: '请输入开户行总行', trigger: 'change' }],
		openingBankCode: [{ required: true, message: '请输入开户行机构代码', trigger: 'change' }],
		openingCity: [{ required: true, message: '请选择开户行省份', trigger: 'change' }],
		openingProvince: [{ required: true, message: '请选择开户行市', trigger: 'change' }],
		accountName: [{ required: true, message: '请输入银行账户名称', trigger: 'blur' }],
		bankCardNumber: [{ required: true, message: '请输入对公账号', trigger: 'blur' }],
		backupClearingAccount: [{ required: true, message: '请选择是否备用清算账户：', trigger: 'change' }],
	},
};
