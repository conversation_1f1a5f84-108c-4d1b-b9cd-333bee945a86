<template>
	<div class="agreement-signing">
		<div class="agreement-content" v-if="props.stepActive == 22 || props.stepActive == 30">
			<div class="w100 mb-[40px]">
				<co-table :config="tableConfig" :header="tableConfig.tableHeader" @loaded="dsTableLoaded">
					<template #name="{ row }">
						<span class="cursor-pointer text-primary" @click="previewAgreement(row)">
							{{ row.name }}
						</span>
					</template>
					<template #signatureStatus="{ row }">
						{{ row.signatureStatus == 0 ? '未签章' : '已签章' }}
					</template>
				</co-table>
			</div>
			<el-button v-if="props.stepActive == 22" class="w-[120px] h-[40px]" :loading="btnLoading" @click="oneSigning" type="primary">一键签署</el-button>
			<el-button v-if="props.stepActive == 30" class="w-[120px] h-[40px]" type="primary" @click="submitExamine" :loading="btnLoading">提交审核</el-button>
		</div>
		<!-- 审核中 --><!-- 审核通过 -->
		<div class="agreement-examine" v-if="props.stepActive == 40 || props.stepActive == 42">
			<finishCom :isShowButton="false" :item="getSuccessData()">
				<template #button>
					<el-button v-if="props.stepActive == 40" class="w-[120px] h-[40px] mt-[40px]" type="primary" plain @click="refresh">刷新</el-button>
					<el-button v-if="props.stepActive == 42" class="w-[120px] h-[40px] mt-[40px]" type="primary" @click="goInvoicing">去开票</el-button>
				</template>
			</finishCom>
		</div>
		<co-preview v-model="previewUrl" width="80%" :title="previewTitle" layer />
		<willingnessDialog ref="willingnessDialogRef" @willingnessCallback="willingnessCallback" />
	</div>
</template>
<script setup lang="ts">
import { getProtocolList, protocolPreview, uploadBatch, filings } from '/@/api/enterprise-record/index';
import { useUserInfo } from '/@/stores/userInfo';
import { ElMessage } from 'element-plus';
import { getFiles } from '/@/api/common/upload.js';
import examineWait from '/@/assets/examine-wait.png';
import examineSuccess from '/@/assets/examine-success.png';
const userInfos = useUserInfo();
const finishCom = defineAsyncComponent(() => import('./finish.vue'));
const willingnessDialog = defineAsyncComponent(() => import('/src/views/supply-chain-bill/components/willingness-dialog.vue'));
const props = defineProps({
	stepActive: {
		type: Number,
	},
});

let onSearch: any = null;
const dsTableLoaded = ({ getDataList }: any) => {
	onSearch = getDataList;
};
const tableConfig = {
	request: {
		apiName: getProtocolList,
		params: {
			ecode: userInfos.userInfos.ecode,
		},
	},
	pagination: false,
	operation: false,
	tableHeader: [
		{ prop: 'name', label: '协议名称', align: 'left' },
		{ prop: 'signatureStatus', label: '签证状态', width: '300', align: 'left' },
	],
};
const btnLoading = ref(false);
const getSuccessData = () => {
	if (props.stepActive == 40) {
		return {
			title: '审核中',
			img: examineWait,
			dec: '',
		};
	} else if (props.stepActive == 42) {
		return {
			title: '审核通过',
			img: examineSuccess,
			dec: '恭喜您备案成功！',
		};
	}
	return {};
};
// 定义子组件向父组件传值/事件
const emit = defineEmits(['changeStep']);
// 一键签署
const willingnessDialogRef = ref();
const oneSigning = () => {
	btnLoading.value = true;
	uploadBatch({ ecode: userInfos.userInfos.ecode })
		.then((res) => {
			if (res.data && res.data.length) {
				willingnessDialogRef.value.openDialog('record', res.data);
			}
		})
		.finally(() => {
			btnLoading.value = false;
		});
};
// 立即验证完成
const willingnessCallback = () => {
	emit('changeStep', 30);
	onSearch();
};
// 提交审核
const submitExamine = () => {
	btnLoading.value = true;
	filings({ ecode: userInfos.userInfos.ecode })
		.then((res) => {
			ElMessage.success(res.msg);
			emit('changeStep', 40);
		})
		.finally(() => {
			btnLoading.value = false;
		});
};
// 刷新
const refresh = () => {
	emit('changeStep', -1);
};
const router = useRouter();
// 去开票
const goInvoicing = () => {
	router.push('/supply-chain-bill/tickets-enroll/index');
};

const previewUrl = ref('');
const previewTitle = ref('');
const previewAgreement = (row: any) => {
	previewTitle.value = row.name;
	if (row.signatureStatus == 0) {
		protocolPreview({ ecode: userInfos.userInfos.ecode, templateKey: row.templateId }).then((res) => {
			if (res.data) {
				getFiles([res.data]).then((res: any) => {
					if (res.data && res.data.length) {
						previewUrl.value = res.data[0].fileUrl;
					} else {
						ElMessage.error('没有查询到附件');
					}
				});
			}
		});
	} else {
		getFiles([row.templateId]).then((res: any) => {
			if (res.data && res.data.length) {
				previewUrl.value = res.data[0].fileUrl;
			} else {
				ElMessage.error('没有查询到附件');
			}
		});
	}
};
</script>
<style lang="scss" scoped>
.agreement-signing {
	width: 100%;
	height: 100%;
	overflow: hidden;
}
.agreement-content,
.agreement-examine {
	width: 100%;
	height: 100%;
	background: #fff;
	border-radius: 4px;
	overflow-y: scroll;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20px;
}
</style>
