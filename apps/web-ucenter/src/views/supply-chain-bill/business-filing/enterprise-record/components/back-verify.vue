<template>
	<div class="back-verify">
		<div class="back-step">
			<el-steps style="width: 800px" :active="active">
				<el-step v-for="(item, index) in stepsList" :key="index">
					<template #icon>
						<span class="step-item">
							<span class="step-item-success" v-if="active > index + 1"><CircleCheck class="w-[30px] h-[30px]" /></span>
							<span class="step-item-active" v-else-if="active === index + 1">{{ index + 1 }}</span>
							<span class="step-item-wait" v-else-if="active < index + 1">{{ index + 1 }}</span>
							<span class="text-[16px] mx-[12px]" v-if="active > index + 1">{{ item.title }}</span>
							<span class="text-[16px] mx-[12px]" v-if="active === index + 1">{{ item.title }}</span>
							<span class="text-[16px] mx-[12px] text-[#909399]" v-if="active < index + 1">{{ item.title }}</span>
						</span>
					</template>
				</el-step>
			</el-steps>
		</div>
		<div class="back-content">
			<el-form ref="ruleFormRef" label-width="150px" :model="ruleForm" :rules="datas.rules" v-if="props.stepActive == 12">
				<el-form-item label="开户行行号：" prop="openingBankNumber">
					<el-input v-model="ruleForm.openingBankNumber" placeholder="请输入开户行行号" clearable ref="inputRef" @focus="handleInputFocus" />
				</el-form-item>
				<el-form-item label="开户行支行：" prop="bankBranch">
					<el-input v-model="ruleForm.bankBranch" disabled placeholder="请输入开户行支行" clearable />
				</el-form-item>
				<el-form-item label="开户行总行：" prop="bankHeadOffice">
					<el-input v-model="ruleForm.bankHeadOffice" disabled placeholder="请输入开户行总行" clearable />
				</el-form-item>
				<el-form-item label="开户行机构代码：" prop="openingBankCode">
					<el-input v-model="ruleForm.openingBankCode" disabled placeholder="请输入开户行机构代码" clearable />
				</el-form-item>
				<el-form-item label="开户行省份：" prop="openingProvince">
					<el-select v-model="ruleForm.openingProvince" placeholder="请选择开户行省份" clearable @change="handleProvince">
						<el-option v-for="item in allAreaList" :key="item.dicId" :label="item.dicName" :value="item.dicValue" />
					</el-select>
				</el-form-item>
				<el-form-item label="开户行市：" prop="openingCity">
					<el-select v-model="ruleForm.openingCity" placeholder="请选择开户行市" clearable>
						<el-option v-for="item in cityList" :key="item.dicId" :label="item.dicName" :value="item.dicValue" />
					</el-select>
				</el-form-item>
				<el-form-item label="银行账户名称：" prop="accountName">
					<el-input v-model="ruleForm.accountName" placeholder="请输入银行账户名称" clearable />
				</el-form-item>
				<el-form-item label="对公账号：" prop="bankCardNumber">
					<el-input v-model="ruleForm.bankCardNumber" placeholder="请输入对公账号" clearable />
				</el-form-item>
				<el-form-item label="是否备用清算账户：" prop="backupClearingAccount">
					<el-select v-model="ruleForm.backupClearingAccount" disabled placeholder="请选择是否备用清算账户：" clearable>
						<el-option label="否" value="0" />
						<el-option label="是" value="1" />
					</el-select>
				</el-form-item>
				<el-form-item class="back-btn">
					<el-button class="w-[120px] h-[40px]" type="primary" :loading="btnLoading" @click="smallPayment">小额打款</el-button>
				</el-form-item>
			</el-form>
			<div class="payment-class" v-if="props.stepActive == 20">
				<h3>账户信息</h3>
				<el-row :gutter="20" align="middle" class="inform-class">
					<el-col :span="12">
						<span class="text-[14px] text-[#86909C] w-[100px] text-right">企业名称：</span>
						<span class="text-[14px] text-[#1D2129]">{{ enterpriseInfoData.enterpriseName || '-' }}</span>
					</el-col>
					<el-col :span="12">
						<span class="text-[14px] text-[#86909C] w-[100px] text-right">提交时间：</span>
						<span class="text-[14px] text-[#1D2129]">{{ enterpriseInfoData.bankAccountDTO.insertTime || '-' }}</span>
					</el-col>
					<el-col :span="12">
						<span class="text-[14px] text-[#86909C] w-[100px] text-right">开户行：</span>
						<span class="text-[14px] text-[#1D2129]">{{ enterpriseInfoData.bankAccountDTO.bankHeadOffice || '-' }}</span>
					</el-col>
					<el-col :span="12">
						<span class="text-[14px] text-[#86909C] w-[100px] text-right">银行账户：</span>
						<span class="text-[14px] text-[#1D2129]">{{ enterpriseInfoData.bankAccountDTO.bankCardNumber || '-' }}</span>
					</el-col>
				</el-row>
				<span class="flex justify-start">
					<h3>小额打款验证</h3>
					<span class="text-[12px] mt-[5px] ml-[10px] text-[#f56c6c]">已成功提交至银行打款，请耐心平台打款，打款预计2小时内到账，请联系财务人员，收到汇款后返回此处确认打款金额，即可完成认证</span>
				</span>
				<div class="user-card">
					<el-form label-width="130px" :model="amountBankForm" ref="paymentFormRef">
						<el-form-item
							label="验证金额："
							prop="amountBank"
							:rules="[
								{ required: true, message: '请输入验证金额', trigger: 'blur' },
								{ validator: rule.amountValidator, trigger: 'blur' },
							]"
						>
							<el-input v-model="amountBankForm.amountBank">
								<template #append>元</template>
							</el-input>
						</el-form-item>
						<el-form-item class="back-btn">
							<div class="flex flex-col text-[12px] items-center">
								<el-button type="primary" class="w-[120px] h-[40px]" :disabled="paymentCount.verifyAmountCounts == 0" @click="verifyAmount">验证金额</el-button>
								<span class="text-[#86909C]"
									>还剩<b class="text-primary">{{ paymentCount.verifyAmountCounts }}</b
									>次机会</span
								>
							</div>
							<div class="flex flex-col ml-[20px] text-[12px] items-center">
								<el-button type="primary" class="w-[120px] h-[40px]" @click="againApply" plain>重新申请</el-button>
								<span class="text-[#86909C]"
									>今日还剩<b class="text-primary">{{ paymentCount.smallPaymentCounts }}</b
									>次机会</span
								>
							</div>
						</el-form-item>
					</el-form>
				</div>
			</div>
			<span v-if="props.stepActive == 20" class="text-[12px] mt-[5px] mt-[50px] text-[#f56c6c]">
				重要提示：企业每日可发起10次小额打款申请，每次申请仅有3次验证的机会，打款金额预计在2小时内到账，并且在7天内保持有效，请确保输入的金额正确再进行验证!
			</span>
			<finishCom :item="successData" v-if="props.stepActive == 21 && active == 3" @nextStep="nextStep" />
		</div>
		<openingBackNumber ref="openingBackNumberRef" @callback="getBankData" />
	</div>
</template>
<script setup lang="ts">
import datas from './index.data';
import { getDictionary } from '/@/api/common/dictionary.js';
import { dictApi } from '/@/api/dict/index';
import { useUserInfo } from '/@/stores/userInfo';
import { saveBankAccount, getCounts, bankVerifyAmount, repayment } from '/@/api/enterprise-record/index';
import { rule } from '/@/utils/validate';
import { ElMessage } from 'element-plus';
const finishCom = defineAsyncComponent(() => import('./finish.vue'));
const openingBackNumber = defineAsyncComponent(() => import('./opening-back-number.vue'));
const props = defineProps({
	stepActive: {
		type: Number,
	},
	enterpriseInfoData: {
		type: Object,
		default: () => {},
	},
});

const successData = {
	title: '银行账户已验证',
	img: '',
	dec: '您已完成银行账户验证！',
};
// 定义子组件向父组件传值/事件
const emit = defineEmits(['changeStep']);
const active = ref(1);
const stepsList = [
	{
		title: '银行信息填写',
	},
	{
		title: '小额打款验证',
	},
	{
		title: '验证完成',
	},
];
// 银行卡认证
const ruleFormRef = ref();
const ruleForm = reactive({
	openingBankNumber: '',
	bankBranch: '',
	bankHeadOffice: '',
	openingBankCode: '',
	openingCity: '',
	openingProvince: '',
	accountName: '',
	bankCardNumber: '',
	backupClearingAccount: '1',
});
// 获取地区字典
const allAreaList = ref();
const cityList = ref();
const getDic = () => {
	// 获取地区
	getDictionary(dictApi.allArea).then((res: any) => {
		allAreaList.value = res;
	});
};
getDic();

// 开户行省份change回调
const handleProvince = (val: string) => {
	if (val) {
		const filterData = allAreaList.value.find((item: DicItem) => item.dicValue === val);
		cityList.value = filterData.children;
	} else {
		ruleForm.openingCity = '';
		cityList.value = {};
	}
};
// 打开选择开户行弹窗
const inputRef = ref<HTMLInputElement>();
const openingBackNumberRef = ref();
const handleInputFocus = (e: FocusEvent) => {
	e.preventDefault();
	if (inputRef.value) {
		inputRef.value.blur(); // 主动失去焦点
	}
	openingBackNumberRef.value.openDialog();
};
interface BankData {
	parentUbankNo: string;
	ubankName: string;
	parentUbankName: string;
	orgNo: string;
}
// 回显开户行部分字段
const getBankData = (val: BankData) => {
	ruleForm.openingBankNumber = val.parentUbankNo;
	ruleForm.bankBranch = val.ubankName;
	ruleForm.bankHeadOffice = val.parentUbankName;
	ruleForm.openingBankCode = val.orgNo;
};
// --------------------

// 小额打款
const btnLoading = ref<boolean>(false);
const userInfos = useUserInfo();
const smallPayment = () => {
	ruleFormRef.value.validate((valid: boolean) => {
		if (!valid) return;
		btnLoading.value = true;
		const params = {
			...ruleForm,
			enterpriseName: userInfos.userInfos.businessName, // 企业名称
			type: 1, // 后台要求写死 - 1企业认证入口
		};
		saveBankAccount(params)
			.then((res) => {
				ElMessage.success(res.msg);
				ruleFormRef.value.resetFields();
				getCountsData(ruleForm.bankCardNumber);
				active.value = 2;
				emit('changeStep', 20);
			})
			.finally(() => {
				btnLoading.value = false;
			});
	});
};
// 从新申请
const againApply = () => {
	let _params = {
		bankCardNumber: props.enterpriseInfoData.bankAccountDTO.bankCardNumber,
		bankAccountId: props.enterpriseInfoData.bankAccountId,
		type: 1,
	};
	repayment(_params).then((res) => {
		if (res.code == 200) {
			handleProvince(res.data.openingProvince);
			res.data.backupClearingAccount = String(res.data.backupClearingAccount); // 备用清算账户(0:否 1:是)
			Object.assign(ruleForm, res.data);
			ElMessage.success(res.msg);
			paymentFormRef.value.resetFields();
			emit('changeStep', 12);
			active.value = 1;
		} else {
			ElMessage.error(res.msg);
		}
	});
};
// 验证金额
const amountBankForm = reactive({
	amountBank: '',
});
const paymentFormRef = ref();
const verifyAmount = () => {
	paymentFormRef.value.validate((vaild: boolean) => {
		if (vaild) {
			let _params = {
				bankCardNumber: props.enterpriseInfoData.bankAccountDTO.bankCardNumber,
				bankAccountId: props.enterpriseInfoData.bankAccountId,
				amountBank: amountBankForm.amountBank,
				type: 1,
			};
			bankVerifyAmount(_params)
				.then((res) => {
					if (res.code == 200) {
						ElMessage.success(res.msg);
						emit('changeStep', 21);
						active.value = 3;
					} else {
						ElMessage.error(res.msg);
					}
				})
				.finally(() => {
					getCountsData(props.enterpriseInfoData.bankAccountDTO.bankCardNumber);
				});
		}
	});
};
const nextStep = () => {
	emit('changeStep', 22);
};
const init = () => {
	switch (props.stepActive) {
		case 11:
			active.value = 1;
			break;
		case 20:
			active.value = 2;
			break;
		case 21:
			active.value = 3;
			break;
	}
	if (props.stepActive == 20) {
		getCountsData(props.enterpriseInfoData.bankAccountDTO.bankCardNumber);
	}
};
// 获取打款次数
const paymentCount = ref({
	smallPaymentCounts: '',
	verifyAmountCounts: '',
});
const getCountsData = (bankNumber: string) => {
	getCounts({ ecode: userInfos.userInfos.ecode, bankNumber }).then((res) => {
		paymentCount.value = res.data;
	});
};
init();
</script>
<style lang="scss" scoped>
.back-verify {
	width: 100%;
	height: 100%;
	overflow: hidden;
}
.back-content {
	width: 100%;
	height: calc(100% - 100px - 20px);
	margin-top: 20px;
	background: #fff;
	border-radius: 4px;
	overflow-y: scroll;
	display: flex;
	align-items: center;
	flex-direction: column;
	padding: 20px;
	.payment-class {
		width: 900px;
		h3 {
			margin-bottom: 20px;
			font-size: 18px;
			// width: 100%;
			display: flex;
			align-items: center;
			font-weight: bold;
			&:before {
				content: '';
				display: inline-block;
				width: 4px;
				height: 20px;
				margin-right: 10px;
				margin-top: -2px;
				background: #165dff;
			}
		}
		.user-card {
			padding: 0 30px;
		}
		.el-row.inform-class {
			margin-bottom: 20px;
			.el-col {
				padding: 0 40px;
				height: 40px;
				display: flex;
				align-items: center;
			}
		}
	}
	:deep(.el-form) {
		width: 600px;
		margin-top: 20px;
		.back-btn .el-form-item__content {
			display: flex;
			justify-content: center;
			margin-left: 0 !important;
		}
	}
}
.back-step {
	width: 100%;
	height: 100px;
	background: #fff;
	border-radius: 4px;
	display: flex;
	justify-content: center;
	align-items: center;
	:deep(.el-steps) {
		.step-item {
			display: flex;
			flex-direction: column;
			align-items: center;
		}
		.el-step__icon {
			width: 40px;
			height: 40px;
		}
		.el-step.is-horizontal .el-step__line {
			top: 8px;
		}
		.step-item-success {
			width: 30px;
			height: 30px;
			// border: 1px solid var(--el-color-primary);
			border-radius: 30px;
			display: flex;
			justify-content: center;
			align-items: center;
		}
		.step-item-active {
			width: 30px;
			height: 30px;
			font-size: 16px;
			background: var(--el-color-primary);
			border: 1px solid var(--el-color-primary);
			color: #fff;
			border-radius: 30px;
			display: flex;
			justify-content: center;
			align-items: center;
		}
		.step-item-wait {
			width: 30px;
			height: 30px;
			font-size: 16px;
			border: 1px solid var(--el-color-info);
			border-radius: 30px;
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
}
</style>
