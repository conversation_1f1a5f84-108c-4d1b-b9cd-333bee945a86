<template>
	<el-dialog title="意愿性确认" v-model="visible" width="800" :z-index="10" :close-on-click-modal="false" draggable @close="closeDialog">
		<el-form v-if="shouldShowForm" ref="willingnessFormRef" :model="dataForm" label-width="140px" style="height: 90px" v-loading="loading">
			<el-form-item label="短信验证码：" prop="code" :rules="[{ required: true, message: '请输入短信验证码', trigger: 'blur' }]">
				<verificationCode v-model="dataForm.code" :verifyCallback="verifyCallback" @callback="callback" />
				<span>短信验证码将发送到{{ encryptPhoneNumber(dataForm.phone) }}上，验证成功后，系统会自动给所有协议签章。</span>
			</el-form-item>
			<el-form-item label="业务办理单预览：" v-if="filedType != 'record' && allData?.signatureFileId">
				<a style="margin-left: 5px; color: #165dff; cursor: pointer" @click="lookFile(allData.signatureFileId)">预览附件</a>
			</el-form-item>
		</el-form>
		<div v-else class="flex flex-col items-center">
			<div class="text-[20px] mb-[8px]">意愿性确认已完成！</div>
			<a class="text-[16px] text-[#165dff] cursor-pointer" @click="lookFile(signData.signatureFileId)">点击预览业务办理单</a>
		</div>
		<template #footer>
			<span class="dialog-footer">
				<el-button @click="closeDialog">取消</el-button>
				<el-button v-if="shouldShowForm" type="primary" :disabled="isDisabled" @click="onSubmit" :loading="loading">立即验证</el-button>
				<el-button v-else type="primary" @click="closeDialog">确认</el-button>
			</span>
		</template>
		<co-preview v-model="previewUrl" width="80%" title="预览附件" layer />
	</el-dialog>
</template>
<script setup lang="ts">
import { encryptPhoneNumber } from '/@/utils/other';
import { ElMessage } from 'element-plus';
import { getFiles } from '/@/api/common/upload.js';
import { contractIntentSigning, postSignBatch } from '/@/api/enterprise-record/index';
import { submitSignature, createSignFile } from '/@/api/supply-chain-bill/signature';
import { useUserInfo } from '/@/stores/userInfo';

const emit = defineEmits(['willingnessCallback']);
const userInfos = useUserInfo();
const verificationCode = defineAsyncComponent(() => import('/@/components/verification-code/index.vue'));
const verifyCallback = () => {
	if (!dataForm.value.phone) {
		ElMessage.error('请输入手机号');
		return false;
	}
	return true;
};
const { AUTH_STATUS, currentAuthStatus, getdetailInfo } = inject('verify');
// const shouldShowForm = computed(() => isRecord || !allData.value.signatureStatus);
const verifyParams = ref();
const isRecord = ref<boolean>(); // 是否为备案
// 验证码回调
const callback = (type: string, beginCountdownCallBack: any) => {
	if (type === 'verifySuccess') {
		// 意愿性签章回调接口
		const _params = {
			phone: dataForm.value.phone,
			socialCode: userInfos.userInfos.ecode,
			fileIdList: isRecord.value
				? allData.value.map((item: any) => item.id) // 备案
				: [allData.value.signatureFileId], // 票据
		};
		contractIntentSigning(_params)
			.then((res) => {
				ElMessage.success(res.msg);
				verifyParams.value = res.data;
				isDisabled.value = false;
				beginCountdownCallBack();
			})
			.catch((error) => {
				beginCountdownCallBack(error);
			});
	}
};
// 定义变量内容
const visible = ref<boolean>(false);
const loading = ref<boolean>(false);
const isDisabled = ref<boolean>(true);
const willingnessFormRef = ref();

// 定义需要的数据
interface DataForm {
	phone: string;
	code: string;
}

const dataForm = ref<DataForm>({
	phone: '',
	code: '',
});

// 预览附件
const previewUrl = ref<string>('');
const lookFile = (id: string) => {
	getFiles([id]).then((res: any) => {
		previewUrl.value = res.data[0]?.fileUrl || '';
	});
};
// 类型
const filedType = ref('');
const allData = ref();
const shouldShowForm = ref(true);
const signData = ref();
// 打开弹框
const openDialog = (filed: string, data: any, signAllData: any) => {
	// filedType.value = filed;
	isRecord.value = filed === 'record';
	dataForm.value.phone = userInfos.userInfos.phone;
	if (isRecord.value) {
		// 备案
		allData.value = data;
	} else {
		// 票据
		if (!signAllData.signId) {
			getSignFile(data);
		} else {
			signData.value = signAllData;
			shouldShowForm.value = !signAllData.signId;
		}
	}
	visible.value = true;
};

// 获取待签章文件
// billId业务签章表id; busiCategory业务类型;
const getSignFile = (data: { reviewUserName?: string; operator?: string; billId: string; busiCategory: string }) => {
	loading.value = true;
	const name = userInfos.userInfos.name;
	const _params = {
		reviewUserName: name,
		operator: name,
		...data,
	};
	createSignFile(_params)
		.then((res) => {
			allData.value = res.data;
		})
		.finally(() => {
			loading.value = false;
		});
};
// 关闭弹框
const closeDialog = () => {
	loading.value = false;
	willingnessFormRef.value?.clearValidate();
	visible.value = false;
};
const onSubmit = () => {
	willingnessFormRef.value.validate((valid: boolean) => {
		if (valid) {
			loading.value = true;
			const _params = {
				wishType: 1, // 意愿认证类型1:个人短信，2:企业短信，3:人脸个人，4:人脸企业
				...dataForm.value,
				...verifyParams.value,
				socialCode: userInfos.userInfos.ecode,
				...(!isRecord.value && { id: allData.value.signId }),
			};
			const api = isRecord.value ? postSignBatch : submitSignature;
			api(_params)
				.then(async (res) => {
					await getdetailInfo(false);
					ElMessage.success(res.msg);
					emit('willingnessCallback', res.data);
					closeDialog();
					currentAuthStatus.value = AUTH_STATUS.AUTHENTICATED;
				})
				.finally(() => {
					loading.value = false;
				});
		}
	});
};

defineExpose({
	openDialog,
});
</script>
<style lang="scss" scoped>
::v-deep.co-verifty-wrapper {
	width: 100%;

	.el-input {
		width: calc(100% - 100px - 8px);
	}

	.code-btn {
		margin-left: 8px;
		width: 100px;
		height: 32px;
		display: flex;
		justify-content: center;
		align-items: center;
		background: #165dff;
		font-size: 14px;
		border-radius: 4px;
		color: #fff;
	}
}
</style>
