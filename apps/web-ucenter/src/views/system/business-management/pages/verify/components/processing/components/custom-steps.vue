<script setup>
// 组件属性定义
const props = defineProps({
	// 当前激活的步骤
	currentStep: {
		type: Number,
		default: 0,
	},
	// 步骤配置数据
	steps: {
		type: Array,
		default: () => [],
	},
	// 是否允许点击切换步骤
	clickable: {
		type: Boolean,
		default: true,
	},
});

// 事件定义
const emit = defineEmits(['step-click']);

// 判断步骤是否为激活状态
const isActiveStep = (stepKey) => {
	return stepKey === props.currentStep;
};

// 判断步骤是否已完成
const isCompletedStep = (stepKey) => {
	return stepKey < props.currentStep;
};

// 获取步骤的样式类
const getStepClass = (stepKey) => {
	if (isActiveStep(stepKey)) {
		return 'step-active';
	} else if (isCompletedStep(stepKey)) {
		return 'step-completed';
	} else {
		return 'step-pending';
	}
};

// 获取连接线的样式类
const getLineClass = (index) => {
	const stepKey = props.steps[index]?.key;
	if (isCompletedStep(stepKey) || isActiveStep(stepKey)) {
		return 'line-active';
	} else {
		return 'line-pending';
	}
};
</script>

<template>
	<div class="custom-steps-container">
		<!-- 步骤条主体 -->
		<div class="steps-wrapper">
			<div v-for="(step, index) in steps" :key="step.key" class="step-item" :class="getStepClass(step.key)">
				<!-- 步骤圆圈和数字 -->
				<div class="step-circle">
					<span class="step-number">{{ step.stepNumber }}</span>
				</div>

				<!-- 步骤标题 -->
				<div class="step-content">
					<div class="step-title">{{ step.title }}</div>
				</div>

				<!-- 连接线（最后一个步骤不显示） -->
				<div v-if="index < steps.length - 1" class="step-line" :class="getLineClass(index)"></div>
			</div>
		</div>
	</div>
</template>

<style scoped lang="scss">
.custom-steps-container {
	@apply w-full bg-white;
	padding: 10px 0;
}

.steps-wrapper {
	@apply flex items-center justify-center relative;
	max-width: 800px;
	margin: 0 auto;
}

.step-item {
	@apply flex flex-col items-center relative;
	flex: 1;
	transition: all 0.3s ease;
}

.step-circle {
	@apply flex items-center justify-center rounded-full;
	width: 35px;
	height: 35px;
	border: 2px solid;
	transition: all 0.3s ease;
	margin-bottom: 12px;
}

.step-number {
	@apply font-semibold text-sm;
}

.step-content {
	@apply text-center;
}

.step-title {
	@apply text-sm font-medium;
	white-space: nowrap;
}

.step-line {
	@apply absolute;
	height: 2px;
	top: 20px;
	left: calc(50% + 20px);
	right: calc(-50% + 20px);
	transition: all 0.3s ease;
}

/* 激活状态样式 */
.step-active {
	.step-circle {
		@apply bg-blue-500 border-blue-500;
	}

	.step-number {
		@apply text-white;
	}

	.step-title {
		@apply text-blue-500;
	}
}

/* 已完成状态样式 */
.step-completed {
	.step-circle {
		@apply bg-green-500 border-green-500;
	}

	.step-number {
		@apply text-white;
	}

	.step-title {
		@apply text-green-600;
	}
}

/* 待处理状态样式 */
.step-pending {
	.step-circle {
		@apply bg-gray-100 border-gray-300;
	}

	.step-number {
		@apply text-gray-500;
	}

	.step-title {
		@apply text-gray-500;
	}
}

/* 连接线样式 */
.line-active {
	@apply bg-blue-500;
}

.line-pending {
	@apply bg-gray-300;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.custom-steps-container {
		padding: 16px 0;
	}

	.step-circle {
		width: 32px;
		height: 32px;
		margin-bottom: 8px;
	}

	.step-number {
		@apply text-xs;
	}

	.step-title {
		@apply text-xs;
	}

	.step-line {
		top: 16px;
		left: calc(50% + 16px);
		right: calc(-50% + 16px);
	}
}
</style>
