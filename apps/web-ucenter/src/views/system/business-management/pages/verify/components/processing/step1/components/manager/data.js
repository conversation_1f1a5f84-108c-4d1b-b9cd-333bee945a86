import { LongTerm, yesOrNoEnum } from '@dict';

export const formListFn = function () {
	return [
		{ id: 'identicalPerson', name: '管理员同法定代表人', type: 'radio', list: yesOrNoEnum.data, value: 0 }, // json
		{ type: 'text' },
		{
			id: 'idPicUp',
			name: '身份证人像面',
			type: 'uploadPic',
			relation: { id: 'identicalPerson', val: 0 },
			upload: { upType: ['png', 'jpg', 'jpeg'], sizeMax: '10' },
		},
		{
			id: 'idPicBack',
			name: '身份证国徽面',
			type: 'uploadPic',
			relation: { id: 'identicalPerson', val: 0 },
			upload: { upType: ['png', 'jpg', 'jpeg'], sizeMax: '10' },
		},
		{
			id: 'idName',
			name: '管理员姓名',
			attributes: { disabled: true },
			relation: { id: 'identicalPerson', val: 0 },
		},
		{
			type: 'text',
			relation: { id: 'identicalPerson', val: 0 },
		},
		{
			id: 'idNumber',
			name: '管理员证件号码',
			validate: 'isIdentityId',
			attributes: { disabled: true },
			relation: { id: 'identicalPerson', val: 0 },
		},
		{
			id: 'idForeverValid',
			name: '身份证有效期',
			type: 'radio',
			list: LongTerm.data,
			value: 0,
			relation: { id: 'identicalPerson', val: 0 },
		}, // json
		{
			id: 'idStartDate',
			name: '身份证有效期开始时间',
			type: 'date',
			relation: { id: 'identicalPerson', val: 0 },
		}, // json
		{
			id: 'idEndDate',
			name: '身份证有效期结束时间',
			type: 'date',
			relation: [
				{
					id: 'idForeverValid',
					val: 0,
				},
				{ id: 'identicalPerson', val: 0 },
			],
		}, // json
	];
};
