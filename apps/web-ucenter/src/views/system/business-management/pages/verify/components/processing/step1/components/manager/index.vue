<template>
	<div class="co-single-form">
		<co-form ref="formRef" :form-list="formList" :form-data="formData" :dictConfig="dictConfig" :labelWidth="labelWidth" @event="onFormEvent"></co-form>
	</div>
</template>

<script setup>
import { ref } from 'vue';
import { formListFn } from './data.js';
import * as dicApi from '@dict';
import { useDicts } from '/@/hooks/useDicts';
import { formFileUpload } from '/@/utils/proFunction';
import { picOCRBack } from '/@/api/enterpriseInfo';
import { HlyIndustry } from '@dict';
import { useMessage } from '/@/hooks/message';
import { encryptedData } from '/@/utils/encrypt';
import { ElMessage } from 'element-plus';

const { verifyData } = inject('verify');
const formRef = ref();
const formList = ref([]);
const formData = ref({});
const labelWidth = ref('178px');
const dictConfig = useDicts(dicApi, HlyIndustry);
formList.value = formListFn();

async function getData(isValid = true) {
	if (formRef.value) {
		let method = isValid ? 'getFormData' : 'getFormDataKey';
		let obj = formRef.value[method]();
		return obj;
	} else {
		return {};
	}
}

watchEffect(() => {
	formData.value = {
		...verifyData.value,
	};
});

async function onFormEvent(val, row) {
	if (val.key == 'idPicBack') {
		if (row.type) {
			let loading = useMessage().loading('身份证国徽面解析中');
			let formData = await formFileUpload({ idPicBack: row }, { needObj: true });
			picOCRBack({ fileUrl: formData.idPicBack.fileUrl, idCardSide: 'back' })
				.then((res) => {
					let { idCardBack } = res.data;
					if (!idCardBack.expiryDate) useMessage().error('未解析到有效数据,请上传清晰图片后重新尝试');
					formRef.value.setFormDataKey('idEndDate', idCardBack.expiryDate.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3'));
					formRef.value.setFormDataKey('idStartDate', idCardBack.issuingDate.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3'));
				})
				.finally(() => {
					useMessage().close(loading);
				});
		}
	} else if (val.key == 'idPicUp') {
		if (row.type) {
			let loading = useMessage().loading('身份证人像面解析中');
			let formData = await formFileUpload({ idPicUp: row }, { needObj: true });
			picOCRBack({ fileUrl: formData.idPicUp.fileUrl, idCardSide: 'front' })
				.then((res) => {
					let { idCardFront } = res.data;
					if (!idCardFront.legalPersonName) useMessage().error('未解析到有效数据,请上传清晰图片后重新尝试');
					formRef.value.setFormDataKey('idNumber', idCardFront.idCardNo);
					formRef.value.setFormDataKey('idName', idCardFront.name);
				})
				.finally(() => {
					useMessage().close(loading);
				});
		}
	}
}

defineExpose({
	getData,
});
</script>

<style lang="scss">
.co-single-form {
	.el-form {
		display: flex;
		flex-wrap: wrap;
	}

	.w-50 {
		.el-form-item__content > div {
			flex-basis: calc(50% - 87.5px);
		}
	}

	.w-30 {
		.el-form-item__content > div {
			flex-basis: 235px;
		}
	}

	.el-form-item {
		flex: 0 0 50%;
	}

	.co-form-block {
		flex: 0 0 100%;
	}

	.basis-full {
		flex-basis: 100%;
	}
}
</style>
