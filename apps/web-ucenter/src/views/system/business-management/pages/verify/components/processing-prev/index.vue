<script setup>
import { ref, inject } from 'vue';
import { useUserInfo } from '/@/stores/userInfo';

const { userInfos } = useUserInfo();
// 获取父组件提供的状态管理
const { currentAuthStatus, AUTH_STATUS } = inject('verify');
import { deleteBusinessOrganization } from '/@/api/enterpriseInfo';
// 企业信息数据（实际项目中应从API获取）
const companyInfo = ref({
	name: userInfos.businessName,
	creditCode: userInfos.ecode,
});
const cancelLoading = ref(false);
// 处理取消认证
const handleCancelAuth = () => {
	cancelLoading.value = true;
	deleteBusinessOrganization({
		notDeleteUserAndUnbinding: true,
		unifiedSocialCreditCode: userInfos.ecode,
	})
		.then(() => {
			currentAuthStatus.value = AUTH_STATUS.UNAUTHENTICATED;
		})
		.finally(() => {
			cancelLoading.value = false;
		});
};

// 处理继续认证
const handleContinueAuth = () => {
	currentAuthStatus.value = AUTH_STATUS.AUTHENTICATING;
};
</script>

<template>
	<!-- 企业认证预处理页面 -->
	<div class="processing-prev-container">
		<!-- 主要内容卡片 -->
		<div class="content-card">
			<!-- 企业信息区域 -->
			<div class="company-info-section">
				<!-- 公司名称 -->
				<h2 class="company-name">{{ companyInfo.name }}</h2>

				<!-- 统一社会信用代码 -->
				<p class="credit-code">{{ companyInfo.creditCode }}</p>
			</div>

			<!-- 状态提示文字 -->
			<div class="status-message">
				<p class="message-text">您的企业正在认证中，请继续完成企业信息认证</p>
			</div>

			<!-- 操作按钮区域 -->
			<div class="action-buttons">
				<el-button type="default" @click="handleCancelAuth" :loading="cancelLoading">取消认证</el-button>
				<el-button type="primary" @click="handleContinueAuth">继续认证</el-button>
			</div>
		</div>
	</div>
</template>

<style scoped lang="scss">
/* 页面容器 - 居中布局 */
.processing-prev-container {
	@apply flex items-center justify-center min-h-full p-8;
	@apply bg-gray-50;
}

/* 主要内容卡片 */
.content-card {
	@apply bg-white rounded-lg shadow-sm;
	@apply flex flex-col items-center;
	@apply px-12 py-16;
	@apply max-w-md w-full;
	@apply relative;
}

.status-text {
	@apply text-white;
}

/* 企业信息区域 */
.company-info-section {
	@apply text-center mb-8 mt-4;
}

/* 公司名称 */
.company-name {
	@apply text-xl font-semibold text-black;
	@apply mb-3;
	margin: 0 0 12px 0;
}

/* 统一社会信用代码 */
.credit-code {
	@apply text-base text-black;
	@apply font-normal;
	margin: 0;
}

/* 状态提示文字 */
.status-message {
	@apply mb-8;
}

.message-text {
	@apply text-base text-black text-center;
	@apply leading-relaxed;
	margin: 0;
}

/* 操作按钮区域 */
.action-buttons {
	@apply flex gap-4;
}

/* 取消认证按钮 */
.cancel-btn {
	@apply px-6 py-2 text-black;
	@apply border border-gray-300 rounded;
	@apply hover:bg-gray-50 transition-colors duration-200;
	@apply cursor-pointer;
	background: white;
	font-size: 14px;
	font-weight: normal;
}

/* 继续认证按钮 */
.continue-btn {
	@apply px-6 py-2 text-white;
	@apply bg-red-500 rounded;
	@apply hover:bg-red-600 transition-colors duration-200;
	@apply cursor-pointer;
	border: none;
	font-size: 14px;
	font-weight: normal;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.processing-prev-container {
		@apply p-4;
	}

	.content-card {
		@apply px-8 py-12;
		@apply max-w-sm;
	}

	.company-name {
		@apply text-lg;
	}

	.credit-code,
	.message-text {
		@apply text-sm;
	}

	.action-buttons {
		@apply flex-col gap-3 w-full;
	}

	.cancel-btn,
	.continue-btn {
		@apply w-full py-3;
	}
}
</style>
