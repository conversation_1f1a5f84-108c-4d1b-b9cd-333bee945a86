<script setup>
import company from './components/company/index.vue';
import legal from './components/legal/index.vue';
import manager from './components/manager/index.vue';
import proTitle from '@components/pro-title/index.vue';
const compantRef = ref();
const legalRef = ref();
const managerRef = ref();
async function getData() {
	return {
		company: await compantRef.value.getData(),
		legal: await legalRef.value.getData(),
		manager: await managerRef.value.getData(),
	};
}
defineExpose({
	getData,
});
</script>

<template>
	<div>
		<proTitle title="企业信息" />
		<company ref="compantRef" />
		<proTitle title="法人信息" />
		<legal ref="legalRef" />
		<proTitle title="管理员信息" />
		<manager ref="managerRef" />
	</div>
</template>

<style scoped lang="scss"></style>
