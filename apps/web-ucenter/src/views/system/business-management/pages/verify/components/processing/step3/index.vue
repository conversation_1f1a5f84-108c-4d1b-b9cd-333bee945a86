<template>
	<div class="agreement-signing">
		<div class="agreement-content">
			<div class="w100 mb-[40px]">
				<co-table :config="tableConfig" :header="tableConfig.tableHeader" @loaded="dsTableLoaded">
					<template #name="{ row }">
						<span class="cursor-pointer text-primary" @click="previewAgreement(row)">
							{{ row.name }}
						</span>
					</template>
					<template #signatureStatus="{ row }">
						{{ row.signatureStatus == 0 ? '未签章' : '已签章' }}
					</template>
				</co-table>
			</div>
			<el-button class="w-[120px] h-[40px]" :loading="btnLoading" @click="oneSigning" type="primary">一键签署</el-button>
		</div>
		<co-preview v-model="previewUrl" width="80%" :title="previewTitle" layer />
		<willingnessDialog ref="willingnessDialogRef" @willingnessCallback="willingnessCallback" />
	</div>
</template>
<script setup lang="ts">
import { authLetter, getEnterprisePreview, enterpriseUploadBatch } from '/@/api/enterprise-record/index';
import { useUserInfo } from '/@/stores/userInfo';
import { getFiles } from '/@/api/common/upload.js';
const userInfos = useUserInfo();
const willingnessDialog = defineAsyncComponent(() => import('./willingness-dialog.vue'));

let onSearch: any = null;
const dsTableLoaded = ({ getDataList }: any) => {
	onSearch = getDataList;
};
const tableConfig = {
	request: {
		apiName: authLetter,
		params: {
			eCode: userInfos.userInfos.ecode,
		},
	},
	pagination: false,
	operation: false,
	tableHeader: [
		{ prop: 'name', label: '协议名称', align: 'left' },
		{ prop: 'signatureStatus', label: '签证状态', width: '300', align: 'left' },
	],
};
const btnLoading = ref(false);

// 定义子组件向父组件传值/事件
const emit = defineEmits(['changeStep']);
// 一键签署
const willingnessDialogRef = ref();
const oneSigning = () => {
	btnLoading.value = true;
	enterpriseUploadBatch({ eCode: userInfos.userInfos.ecode })
		.then((res) => {
			if (res.data && res.data.length) {
				willingnessDialogRef.value.openDialog('record', res.data);
			}
		})
		.finally(() => {
			btnLoading.value = false;
		});
};
// 立即验证完成
const willingnessCallback = () => {
	emit('changeStep', 30);
	onSearch();
};

const previewUrl = ref('');
const previewTitle = ref('');
const previewAgreement = (row: any) => {
	previewTitle.value = row.name;
	if (row.signatureStatus == 0) {
		getEnterprisePreview({ eCode: userInfos.userInfos.ecode, templateId: row.templateId }).then((res) => {
			if (res.data) {
				getFiles([res.data]).then((res: any) => {
					if (res.data && res.data.length) {
						previewUrl.value = res.data[0].fileUrl;
					} else {
						ElMessage.error('没有查询到附件');
					}
				});
			}
		});
	} else {
		getFiles([row.templateId]).then((res: any) => {
			if (res.data && res.data.length) {
				previewUrl.value = res.data[0].fileUrl;
			} else {
				ElMessage.error('没有查询到附件');
			}
		});
	}
};
</script>
<style lang="scss" scoped>
.agreement-signing {
	width: 100%;
	height: 100%;
	overflow: hidden;
}
.agreement-content,
.agreement-examine {
	width: 100%;
	height: 100%;
	background: #fff;
	border-radius: 4px;
	overflow-y: scroll;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20px;
}
</style>
