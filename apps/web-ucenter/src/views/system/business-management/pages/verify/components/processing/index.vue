<script setup>
import { STEP_STATUS, STEP_MAP, STEP_CONFIG, componentMap } from './enum';
import { saveOrUpdate, captchaCodeCheck } from '/@/api/enterpriseInfo';
import CustomSteps from './components/custom-steps.vue';
import { formFileUpload } from '/@/utils/proFunction';
import { useUserInfo } from '/@/stores/userInfo';
// 当前认证状态 - 响应式状态管理
const { currentAuthStatus, processingAuthStatus, AUTH_STATUS, verifyData, getdetailInfo } = inject('verify');
const currentComponentRef = ref();
// 当前要渲染的组件 - 基于状态自动计算
const currentComponent = computed(() => {
	return componentMap[processingAuthStatus.value];
});
const submitOneLoading = ref(false);
// 处理步骤点击事件
const handleStepClick = (stepKey) => {
	processingAuthStatus.value = stepKey;
};
const userInfo = useUserInfo().userInfos;
const setUserInfos = useUserInfo().setUserInfos;

// 向子组件提供状态管理
provide('processing', {
	processingAuthStatus,
	STEP_STATUS,
	STEP_MAP,
});

// 管理员字段同步法人
function syncField(obj) {
	if (obj.identicalPerson) {
		obj.idPicUp = obj.legalCertificateFront;
		obj.idPicBack = obj.legalCertificateBack;
		obj.idName = obj.legalPersonName;
		obj.idNumber = obj.legalPersonIdCode;
		obj.idForeverValid = obj.legalCertificateDate;
		obj.idStartDate = obj.legalCertificateStartDate;
		obj.idEndDate = obj.legalCertificateEndDate;
	}
	return obj;
}

function submitOne() {
	currentComponentRef.value.getData().then(async (saveData) => {
		submitOneLoading.value = true;
		let obj = {
			...saveData.company,
			...saveData.legal,
			...saveData.manager,
			processingAuthStatus: 1,
			currentAuthStatus: currentAuthStatus.value,
			notDeleteUserAndUnbinding: true, //固定值-宗博
			idPhone: userInfo?.phone || '',
			notSubmit: true,
		};
		let formData = await formFileUpload(obj);
		formData = syncField(formData);
		formData.extendedFields = JSON.stringify(formData);
		// 验证码校验
		try {
			let captchaCodeCheckResult = await captchaCodeCheck({
				captcha: obj.legalPersonVerifyCode,
				captchaKey: obj.legalPersonVerifyCodeCaptchaKey,
			});
			if (captchaCodeCheckResult.code != 200) {
				ElMessage.error(captchaCodeCheckResult.msg);
				submitOneLoading.value = false;
				return;
			}
		} catch (error) {
			submitOneLoading.value = false;
			return;
		}
		saveOrUpdate(formData)
			.then(async (res) => {
				await getdetailInfo(false);
				await setUserInfos();
				verifyData.value = { ecode: res.data.ecode, ...verifyData.value, ...formData };
				handleStepClick(processingAuthStatus.value + 1);
			})
			.finally(() => {
				submitOneLoading.value = false;
			});
	});
}
</script>

<template>
	<div class="business-verify-container">
		<!-- 头部：步骤条区域 -->
		<div class="header-section" v-if="processingAuthStatus !== AUTH_STATUS.AUTHENTICATED">
			<div class="steps-header">
				<h2 class="page-title">企业认证</h2>
				<button class="return-btn">
					<span class="return-text" @click="currentAuthStatus = AUTH_STATUS.UNAUTHENTICATED">返回</span>
				</button>
			</div>
			<el-divider></el-divider>
			<CustomSteps :current-step="processingAuthStatus" :steps="STEP_CONFIG" />
		</div>

		<!-- 内容区域：动态组件 -->
		<div class="content-section">
			<div class="component-wrapper">
				<component :is="currentComponent" class="w-full" ref="currentComponentRef" />
			</div>
		</div>

		<!-- 底部：操作按钮区域 -->
		<div class="footer-section" v-if="processingAuthStatus !== AUTH_STATUS.AUTHENTICATED">
			<div class="button-group">
				<el-button type="primary" v-if="processingAuthStatus === STEP_STATUS.step2" @click="handleStepClick(processingAuthStatus - 1)">上一步 </el-button>
				<el-button :loading="submitOneLoading" type="primary" v-if="processingAuthStatus === STEP_STATUS.step1" @click="submitOne">下一步 </el-button>
				<!--				<el-button type="primary" v-if="processingAuthStatus === STEP_STATUS.step3">一键签署</el-button>-->
			</div>
		</div>
	</div>
</template>
<style>
.el-divider {
	margin: 0 0 9px 0;
}
</style>
<style scoped lang="scss">
.business-verify-container {
	@apply w-full h-full flex flex-col;
	height: 86.9vh;
}

/* 头部区域样式 */
.header-section {
	@apply bg-white border-b border-gray-200;
	flex-shrink: 0;
	margin-bottom: 3px;
}

.steps-header {
	@apply flex items-center justify-between px-6 py-3;
}

.page-title {
	@apply text-xl font-semibold text-gray-800;
	margin: 0;
}

.return-btn {
	@apply flex items-center px-4 py-2 text-gray-600 hover:text-gray-800;
	background: none;
	border: none;
	cursor: pointer;
	transition: color 0.2s ease;
}

.return-text {
	@apply text-sm;
}

/* 内容区域样式 */
.content-section {
	@apply flex-1 overflow-y-auto;
	background-color: #fff;
}

.component-wrapper {
	@apply relative w-full h-full p-6;
}

/* 底部操作区域样式 */
.footer-section {
	@apply bg-white border-t border-gray-200 px-6 py-2 flex items-center justify-center;
	flex-shrink: 0;
	margin-top: 5px;
}

.button-group {
	@apply flex items-center justify-center gap-4;
}

/* 按钮样式 */
.btn-primary {
	@apply px-6 py-2 bg-blue-500 text-white rounded-lg;
	@apply hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
	@apply transition-colors duration-200;
	border: none;
	cursor: pointer;
	font-size: 14px;
	font-weight: 500;
}

.btn-secondary {
	@apply px-6 py-2 bg-gray-100 text-gray-700 rounded-lg;
	@apply hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
	@apply transition-colors duration-200;
	border: none;
	cursor: pointer;
	font-size: 14px;
	font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.steps-header {
		@apply px-4 py-3;
	}

	.page-title {
		@apply text-lg;
	}

	.component-wrapper {
		@apply p-4;
	}

	.footer-section {
		@apply px-4 py-3;
	}

	.button-group {
		@apply gap-3;
	}

	.btn-primary,
	.btn-secondary {
		@apply px-4 py-2 text-sm;
	}
}
</style>
