<template>
	<div class="co-single-form">
		<co-form ref="formRef" :form-list="formList" :form-data="formData" :dictConfig="dictConfig" :labelWidth="labelWidth" @event="onFormEvent">
			<template #legalPersonVerifyCode="{ data }">
				<CoVerify
					class="co-verifty-wrapper"
					v-model="data.legalPersonVerifyCode"
					:verify-callback="verifyPhone"
					:captcha-api-params="{
						checkUserExist: false,
						shouldUserExist: false,
						mobile: legalPersonPhone,
						captchaKey: data.legalPersonVerifyCodeCaptchaKey,
					}"
				/>
			</template>
		</co-form>
	</div>
</template>

<script setup>
import { ref } from 'vue';
import { formListFn } from './data.js';
import * as dicApi from '@dict';
import { useDicts } from '/@/hooks/useDicts';
import { formFileUpload } from '/@/utils/proFunction';
import { picOCRBack } from '/@/api/enterpriseInfo';
import { HlyIndustry } from '@dict';
import { useMessage } from '/@/hooks/message';
import { encryptedData } from '/@/utils/encrypt';

const { verifyData } = inject('verify');

const formRef = ref();
const formList = ref([]);
const formData = ref({});
const labelWidth = ref('178px');
const dictConfig = useDicts(dicApi, HlyIndustry);
formList.value = formListFn();
let legalPersonPhone = ref('');
watchEffect(() => {
	formData.value = {
		...verifyData.value,
	};
	if (verifyData.value?.legalPersonPhone) {
		formRef.value && formRef.value.setFormDataKey('legalPersonVerifyCodeCaptchaKey', encryptedData(verifyData.value.legalPersonPhone || ''));
	}
});

async function getData(isValid = true) {
	if (formRef.value) {
		let method = isValid ? 'getFormData' : 'getFormDataKey';
		let obj = formRef.value[method]({
			setIds: ['legalPersonVerifyCodeCaptchaKey'],
		});
		return obj;
	} else {
		return {};
	}
}

async function onFormEvent(val, row) {
	// 手机号
	if (val.key == 'legalPersonPhone') {
		let obj = await getData(false);
		legalPersonPhone.value = obj.legalPersonPhone;
		formRef.value.setFormDataKey('legalPersonVerifyCodeCaptchaKey', encryptedData(legalPersonPhone || ''));
	}
	if (val.key == 'legalCertificateBack') {
		if (row.type) {
			let loading = useMessage().loading('身份证国徽面解析中');
			let formData = await formFileUpload({ legalCertificateBack: row }, { needObj: true });
			picOCRBack({ fileUrl: formData.legalCertificateBack.fileUrl, idCardSide: 'back' })
				.then((res) => {
					let { idCardBack } = res.data;
					if (!idCardBack.expiryDate) useMessage().error('未解析到有效数据,请上传清晰图片后重新尝试');
					formRef.value.setFormDataKey('legalCertificateEndDate', idCardBack.expiryDate.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3 00:00:00'));
					formRef.value.setFormDataKey('legalCertificateStartDate', idCardBack.issuingDate.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3 00:00:00'));
				})
				.finally(() => {
					useMessage().close(loading);
				});
		}
	} else if (val.key == 'legalCertificateFront') {
		if (row.type) {
			let loading = useMessage().loading('身份证人像面解析中');
			let formData = await formFileUpload({ legalCertificateFront: row }, { needObj: true });
			picOCRBack({ fileUrl: formData.legalCertificateFront.fileUrl, idCardSide: 'front' })
				.then((res) => {
					let { idCardFront } = res.data;
					if (!idCardFront.legalPersonName) useMessage().error('未解析到有效数据,请上传清晰图片后重新尝试');
					formRef.value.setFormDataKey('legalPersonIdCode', idCardFront.idCardNo);
					formRef.value.setFormDataKey('legalPersonName', idCardFront.name);
				})
				.finally(() => {
					useMessage().close(loading);
				});
		}
	}
}

const verifyPhone = async () => {
	let obj = await getData(false);
	if (!obj.legalPersonPhone) {
		ElMessage.error('请输入手机号');
		return false;
	}
	return true;
};
defineExpose({
	getData,
});
</script>

<style lang="scss">
.co-single-form {
	.el-form {
		display: flex;
		flex-wrap: wrap;
	}

	.w-50 {
		.el-form-item__content > div {
			flex-basis: calc(50% - 87.5px);
		}
	}

	.w-30 {
		.el-form-item__content > div {
			flex-basis: 235px;
		}
	}

	.el-form-item {
		flex: 0 0 50%;
	}

	.co-form-block {
		flex: 0 0 100%;
	}

	.basis-full {
		flex-basis: 100%;
	}
}
</style>
