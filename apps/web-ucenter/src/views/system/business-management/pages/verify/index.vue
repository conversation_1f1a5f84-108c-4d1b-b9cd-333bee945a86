<script setup>
import { AUTH_STATUS, STATUS_COMPONENT_MAP, componentMap } from './enum';
import { businessQueryCode } from '/@/api/enterpriseInfo';
import { useUserInfo } from '/@/stores/userInfo';

const isLoad = ref(false);
const router = useRouter();
const verifyData = ref();
// 主进度 AUTH_STATUS
const currentAuthStatus = ref(0);
// 认证中的状态 STEP_STATUS
const processingAuthStatus = ref(0);
const userInfo = useUserInfo().userInfos;
// 当前要渲染的组件 - 基于状态自动计算
const currentComponent = computed(() => {
	return componentMap[currentAuthStatus.value];
});
provide('verify', {
	processingAuthStatus,
	currentAuthStatus,
	AUTH_STATUS,
	STATUS_COMPONENT_MAP,
	verifyData,
	getdetailInfo,
});
onBeforeMount(async () => {
	await getdetailInfo();
});

async function getdetailInfo(isJump = true) {
	if (userInfo.ecode) {
		return businessQueryCode(userInfo.ecode).then((res) => {
			isLoad.value = true;
			if (res.data.extendedFields) {
				verifyData.value = { id: res.data.id, ...JSON.parse(res.data.extendedFields) };
				if (isJump) {
					if (verifyData.value.currentAuthStatus > AUTH_STATUS.AUTHENTICATED) {
						router.replace('/system/business-management/pages/detail/index');
					} else {
						currentAuthStatus.value = verifyData.value.currentAuthStatus;
						processingAuthStatus.value = verifyData.value.processingAuthStatus;
					}
				}
			}
		});
	} else {
		isLoad.value = true;
	}
}
</script>

<template>
	<div class="business-verify-container" v-if="isLoad">
		<div class="component-wrapper">
			<component :is="currentComponent" class="w-full" />
		</div>
	</div>
</template>

<style scoped lang="scss">
.business-verify-container {
	@apply w-full min-h-96 h-full;
}

.component-wrapper {
	@apply relative w-full h-full;
}
</style>
