<template>
	<div class="co-single-form">
		<co-form ref="formRef" :form-list="formList" :form-data="formData" :dictConfig="dictConfig" :labelWidth="labelWidth" @event="onFormEvent">
			<template #foundAddress="{ data }">
				<pro-province-selector :checkStrictly="false" v-model="data.foundAddress" @region-change="(regionData) => (data.address = regionData.regionName)" />
			</template>

			<template #businessLicense>
				<el-button link type="primary">
					<span @click="visible = true">查看示例</span>
				</el-button>
			</template>
			<template #name>
				<span class="text-red-500 ml-1">
					{{ existText }}
				</span>
			</template>
		</co-form>
		<el-dialog v-model="visible" width="800" @closed="visible = false">
			<div class="flex justify-center">
				<el-image :src="businessLicensePng" fit="fill"></el-image>
			</div>
		</el-dialog>
	</div>
</template>

<script setup>
import ProProvinceSelector from '@components/pro-province-selector';
import businessLicensePng from './img/businessLicense.png';
import { ref } from 'vue';
import { formListFn } from './data.js';
import * as dicApi from '@dict';
import { useDicts } from '/@/hooks/useDicts';
import { formFileUpload } from '/@/utils/proFunction';
import { postBusinessLicense, getBusinessByCode } from '/@/api/enterpriseInfo';
import { HlyIndustry } from '@dict';
import { useMessage } from '/@/hooks/message';
import { parseChineseDate } from '/@/utils/formatTime';
import { desensitize } from '/@/utils/desensitize';
import { useUserInfo } from '/@/stores/userInfo';

const { verifyData } = inject('verify');

const visible = ref(false);
const existText = ref('');
const formRef = ref();
const formList = ref([]);
const formData = ref({});
const labelWidth = ref('175px');
const dictConfig = useDicts(dicApi, HlyIndustry);
formList.value = formListFn();
watchEffect(() => {
	formData.value = {
		...verifyData.value,
	};
});
const userInfo = useUserInfo().userInfos;

async function getData() {
	let obj = await formRef.value.getFormData({ setIds: ['address'] });
	return { ...obj, existText: existText.value };
}

async function onFormEvent(val, row) {
	if (val.key == 'businessLicense') {
		if (row.type) {
			let loading = useMessage().loading('营业执照解析中');
			let formData = await formFileUpload({ businessLicense: row }, { needObj: true });
			if (userInfo.ecode) {
				getBusinessByCode(userInfo.ecode).then((res) => {
					if (res.data.extendedFields) {
						let obj = JSON.parse(res.data.extendedFields);
						if (obj.grantAuthStatus) {
							existText.value = `当前企业已认证，请联系管理员（${desensitize(obj.idName, 'name')} ${desensitize(obj.idPhone, 'phone')}）为您创建专属账号`;
						}
					}
				});
			}
			postBusinessLicense({ fileUrl: formData.businessLicense.fileUrl })
				.then((res) => {
					let { businessAddress, validToDate, validFromDate, businessName, unifiedSocialCreditCode } = res.data;
					formRef.value.setFormDataKey('unifiedSocialCreditCode', unifiedSocialCreditCode);
					formRef.value.setFormDataKey('name', businessName);
					formRef.value.setFormDataKey('businessStartTime', parseChineseDate(validFromDate));
					formRef.value.setFormDataKey('businessEndTime', parseChineseDate(validToDate));
					formRef.value.setFormDataKey('detailedAddress', businessAddress);
				})
				.finally(() => {
					useMessage().close(loading);
				});
		} else {
			formData.value = {};
		}
	}
}

defineExpose({
	getData,
});
</script>

<style lang="scss">
.co-single-form {
	.el-form {
		display: flex;
		flex-wrap: wrap;
	}

	.w-50 {
		.el-form-item__content > div {
			flex-basis: calc(50% - 87.5px);
		}
	}

	.w-30 {
		.el-form-item__content > div {
			flex-basis: 235px;
		}
	}

	.el-form-item {
		flex: 0 0 50%;
	}

	.co-form-block {
		flex: 0 0 100%;
	}

	.basis-full {
		flex-basis: 100%;
	}
}
</style>
