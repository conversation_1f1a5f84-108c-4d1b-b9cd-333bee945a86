<script setup>
let { currentAuthStatus, AUTH_STATUS } = inject('verify');

function goAuth() {
	currentAuthStatus.value = AUTH_STATUS.AUTHENTICATING;
}
</script>

<template>
	<!-- 未认证状态提示页面 -->
	<div class="no-start-container">
		<!-- 警告图标 -->
		<div class="warning-icon-wrapper"></div>

		<!-- 提示文字 -->
		<div class="message-text">您还未认证企业，请填写企业信息完成认证</div>

		<!-- 操作按钮 -->
		<div class="action-button-wrapper">
			<el-button type="primary" size="large" @click="goAuth" class="auth-button"> 前往认证企业</el-button>
		</div>
	</div>
</template>

<style scoped lang="scss">
/* 未认证状态页面容器 */
.no-start-container {
	@apply flex flex-col items-center justify-center min-h-full p-8;
	@apply bg-gray-50 rounded-lg;
}

/* 警告图标容器 */
.warning-icon-wrapper {
	@apply mb-12;
	width: 100px;
	height: 100px;
	background: url('./img/warning.png') no-repeat center;
	background-size: contain;
}

/* 警告图标样式 */
.warning-icon {
	@apply text-red-500;
}

/* 提示文字样式 */
.message-text {
	@apply text-lg text-gray-700 text-center mb-12 max-w-md;
	@apply leading-relaxed;
}

/* 操作按钮容器 */
.action-button-wrapper {
	@apply flex justify-center;
}

/* 认证按钮样式 */
.auth-button {
	@apply px-8 py-3 text-base font-medium;
	@apply hover:shadow-light transition-all duration-200;
}
</style>
