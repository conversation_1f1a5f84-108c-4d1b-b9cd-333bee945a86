<script setup>
import { useUserInfo } from '/@/stores/userInfo';
import { verifyEnterpriseInfo, enterpriseRealView, createUserAndNotSmsSend, personAuthInterface, putBusiness, verifyPersonalInfo, personRealView } from '/@/api/enterpriseInfo';
import { ElMessage } from 'element-plus';
import { useEnvStore } from '/@/stores/env';
// 认证状态管理 - 控制页面显示状态
const isAuthenticated = ref(false);
// 检查认证状态的loading状态 - 控制遮罩层显示
const checkLoading = ref(false);
// 首次加载标识 - 控制loading只在第一次加载时显示
const isFirstLoad = ref(true);
const route = useRoute();
const userInfo = useUserInfo().userInfos;
const submitLoading = ref(false);
const authLoading = ref(false);
const { processingAuthStatus, verifyData } = inject('verify');
const { STEP_STATUS } = inject('processing');
let timer = null;
let isCompanyOk = ref(false);
let isPersonOk = ref(false);
const isPersonAuth = ref(false);
const isCompanyAuth = ref(false);
const pending = ref(false);
const webEnv = useEnvStore().env;
// 切换到认证完成状态
const handleAuthenticate = async () => {
	let isCompanyOkAndPersonNotOk = isCompanyOk.value && !isPersonOk.value;
	let method = isCompanyOkAndPersonNotOk ? personRealView : enterpriseRealView;
	authLoading.value = true;
	await createUser();
	let obj = {
		redirectConfig: {
			redirectUrl: location.href + '?verify=true',
		},
		expandParam: JSON.stringify({ localUserId: userInfo.userId }),
		authorizeConfig: {
			authorizedScopes: ['get_org_identity_info', 'org_initiate_sign', 'manage_org_resource', 'get_psn_identity_info', 'psn_initiate_sign', 'manage_psn_resource'],
		},
		socialCode: userInfo.ecode,
		callBackUrl: webEnv.VUE_APP_EQ_CALLBCK,
		phone: userInfo.phone,
		enterpriseName: verifyData.value.name,
	};
	if (isCompanyOkAndPersonNotOk) {
		try {
			await ElMessageBox.confirm('您还未在智安e签平台进行个人认证授权，请先进行个人认证，点击确定后跳转至智安e签完成认证');
		} catch (err) {
			authLoading.value = false;
			return;
		}
	}
	method(obj)
		.then(({ data }) => {
			window.location.href = data.pcUrl;
		})
		.finally(() => {
			authLoading.value = false;
		});
};
onMounted(() => {
	if (route.query.verify) {
		pending.value = true;
	}
	checkVerifyEnterpriseInfoTimer();
});

function checkVerifyEnterpriseInfoTimer() {
	checkVerifyEnterpriseInfo();
	timer = setInterval(() => {
		checkVerifyEnterpriseInfo();
	}, 4000);
}

async function checkVerifyEnterpriseInfo() {
	// 只在首次加载时设置loading状态，阻止用户操作
	if (isFirstLoad.value) {
		checkLoading.value = true;
	}

	try {
		// 并行执行企业认证和个人认证状态检查
		const [enterpriseResult, personalResult] = await Promise.allSettled([
			// 检查企业认证状态
			verifyEnterpriseInfo({ searchWord: userInfo.ecode }),
			// 检查个人认证状态
			verifyPersonalInfo({
				socialCode: userInfo.ecode,
				personalInfoList: [{ idCard: verifyData.value.idNumber, name: verifyData.value.idName, phone: userInfo.phone }],
			}),
		]);

		// 处理企业认证结果
		if (enterpriseResult.status === 'fulfilled') {
			const {
				data: { authorizeUserInfo, authStatus },
			} = enterpriseResult.value;
			if (authStatus == 'SUCCESS') isCompanyAuth.value = true;
			if (authorizeUserInfo && authStatus == 'SUCCESS') isCompanyOk.value = true;
		} else {
			console.error('企业认证状态检查失败:', enterpriseResult.reason);
		}

		// 处理个人认证结果
		if (personalResult.status === 'fulfilled') {
			const { data } = personalResult.value;
			const { authorizeUserInfo, authStatus } = data[0];
			if (authStatus == 'SUCCESS') isPersonAuth.value = true;
			if (authorizeUserInfo && authStatus == 'SUCCESS') isPersonOk.value = true;
		} else {
			console.error('个人认证状态检查失败:', personalResult.reason);
		}

		// 如果企业和个人都已认证，调用用户授权接口
		if (isCompanyAuth.value && isPersonAuth.value) {
			try {
				await personAuthInterface({
					socialCode: userInfo.ecode,
					phone: userInfo.phone,
					name: verifyData.value.idName,
					idCard: verifyData.value.idNumber,
				});
			} catch (error) {
				console.error('用户授权接口调用失败:', error);
			}
		}

		// 检查是否完成所有认证
		if (isCompanyOk.value && isPersonOk.value) {
			isAuthenticated.value = true;
			clearInterval(timer);
		}
	} catch (error) {
		console.error('认证状态检查过程中发生错误:', error);
	} finally {
		// 确保loading状态被清除
		if (isFirstLoad.value) {
			checkLoading.value = false;
		}
		// 重置首次加载标识，后续轮询不再显示loading
		isFirstLoad.value = false;
	}
}

async function createUser() {
	const _params = {
		validateRealNameType: '0',
		phone: userInfo.phone,
		realName: verifyData.value.idName,
		idCard: verifyData.value.idNumber,
		roles: 0,
		type: '1',
		socialCode: userInfo.ecode,
	};
	return createUserAndNotSmsSend(_params).then((res) => {
		return res.data;
	});
}

// 确定并继续操作
const handleContinue = () => {
	submitLoading.value = true;
	let extendedFields = verifyData.value;
	extendedFields.processingAuthStatus = STEP_STATUS.step3;
	extendedFields.authStatus = true; // 认证状态
	extendedFields.grantAuthStatus = true; // 授权状态
	putBusiness({ ...verifyData.value, extendedFields: JSON.stringify(extendedFields) })
		.then((res) => {
			ElMessage.success('操作成功');
			processingAuthStatus.value = STEP_STATUS.step3;
		})
		.finally(() => {
			submitLoading.value = false;
		});
};
onBeforeUnmount(() => {
	clearInterval(timer);
});
</script>

<template>
	<!-- 企业认证状态页面容器 -->
	<div class="enterprise-auth-container">
		<!-- Loading 遮罩层 -->
		<div v-if="checkLoading && !isAuthenticated" class="loading-overlay">
			<div class="loading-content">
				<div class="loading-spinner"></div>
				<p class="loading-text">正在检查认证状态...</p>
			</div>
		</div>

		<!-- 企业认证授权状态 -->
		<div v-if="!isAuthenticated" class="auth-pending-state">
			<!-- 图标区域 -->
			<div class="icon-section">
				<!-- 文档图标 -->
				<div class="document-icon">
					<img src="./img/u4077.png" alt="企业认证文档" class="document-image" />
				</div>
			</div>

			<!-- 标题 -->
			<h2 class="auth-title">企业认证授权</h2>

			<!-- 说明文字 -->
			<div class="auth-description">
				<p class="description-text">企业认证过程中需要在线签署授权函，需要您在智安e签电子签章平台完成认证并授权；点击发起授权按钮后，系统将自动跳转至智安e签平台进行企业认证授权</p>
			</div>

			<!-- 测试按钮 - 模拟认证完成 -->
			<div class="test-action">
				<el-button type="primary" @click="handleAuthenticate" :loading="authLoading">发起认证并授权</el-button>
			</div>
		</div>

		<!-- 企业已认证状态 -->
		<div v-else class="auth-completed-state">
			<!-- 认证完成图标 -->
			<div class="success-icon-section">
				<div class="success-icon-wrapper">
					<img src="./img/u4010.png" alt="企业已认证" class="success-image" />
				</div>
			</div>

			<!-- 标题 -->
			<h2 class="success-title">企业已认证</h2>

			<!-- 成功信息 -->
			<div class="success-message">
				<p class="success-text">您已完成企业授权！</p>
			</div>

			<!-- 操作按钮 -->
			<div class="action-section">
				<el-button type="primary" @click="handleContinue" :loading="submitLoading">确定并继续</el-button>
			</div>
		</div>
	</div>
</template>

<style scoped lang="scss">
/* 企业认证页面主容器 */
.enterprise-auth-container {
	@apply flex flex-col items-center justify-center;
	@apply min-h-full px-8 py-12;
	@apply bg-white relative;
	width: 100%;
	height: 100%;
}

/* Loading 遮罩层样式 */
.loading-overlay {
	@apply absolute inset-0 bg-white bg-opacity-80;
	@apply flex items-center justify-center;
	@apply z-50;
}

.loading-content {
	@apply flex flex-col items-center;
}

.loading-spinner {
	@apply w-8 h-8 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin;
	@apply mb-4;
}

.loading-text {
	@apply text-sm text-gray-600;
	margin: 0;
}

/* 企业认证授权状态样式 */
.auth-pending-state {
	@apply flex flex-col items-center text-center;
	@apply max-w-xl w-full;
}

/* 图标区域 */
.icon-section {
	@apply relative mb-8;
	@apply flex items-center justify-center;
}

/* 文档图标 */
.document-icon {
	@apply relative;

	.document-image {
		@apply w-[375px] object-contain;
	}
}

/* 信息提示图标 */
.info-icon {
	@apply absolute -bottom-2 -right-2;
	@apply w-8 h-8;

	.info-svg {
		@apply w-full h-full;
		filter: drop-shadow(0 2px 8px rgba(79, 126, 255, 0.3));
	}
}

/* 认证标题 */
.auth-title {
	@apply text-xl font-semibold text-black;
	@apply mb-6;
	margin: 0 0 24px 0;
}

/* 说明文字区域 */
.auth-description {
	@apply mb-8;

	.description-text {
		@apply text-sm text-black leading-relaxed;
		@apply px-4;
		margin: 0;
		line-height: 1.6;
	}
}

/* 企业已认证状态样式 */
.auth-completed-state {
	@apply flex flex-col items-center text-center;
	@apply max-w-md w-full;
}

/* 成功图标区域 */
.success-icon-section {
	@apply mb-8;
}

.success-icon-wrapper {
	@apply relative flex items-center justify-center;

	.success-image {
		@apply w-[375px] object-contain;
	}
}

/* 认证成功标记 */
.check-mark {
	@apply absolute -bottom-2 -right-2;
	@apply w-8 h-8;

	.check-svg {
		@apply w-full h-full;
		filter: drop-shadow(0 2px 8px rgba(79, 126, 255, 0.3));
	}
}

/* 成功标题 */
.success-title {
	@apply text-xl font-semibold text-black;
	@apply mb-6;
	margin: 0 0 24px 0;
}

/* 成功信息 */
.success-message {
	@apply mb-8;

	.success-text {
		@apply text-base text-black;
		margin: 0;
	}
}

/* 操作按钮区域 */
.action-section {
	@apply w-full;

	.continue-button {
		@apply w-full px-8 py-3;
		@apply bg-red-500 text-white rounded;
		@apply hover:bg-red-600 transition-colors;
		@apply font-medium;
		@apply focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50;
	}
}

/* 响应式设计 - 适配不同屏幕尺寸 */
@media (max-width: 768px) {
	.enterprise-auth-container {
		@apply px-4 py-8;
	}

	.auth-pending-state,
	.auth-completed-state {
		@apply max-w-sm;
	}

	.document-image,
	.success-image {
		@apply w-20 h-20;
	}

	.auth-title,
	.success-title {
		@apply text-lg;
	}

	.description-text {
		@apply text-xs px-2;
	}
}
</style>
