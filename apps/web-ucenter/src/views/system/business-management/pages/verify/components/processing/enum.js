import { defineAsyncComponent } from 'vue';

// 认证状态枚举 - 对应components目录下的组件
export const STEP_STATUS = {
	step1: 0,
	step2: 1,
	step3: 2,
};

// 状态与组件名称映射关系
export const STEP_MAP = {
	[STEP_STATUS.step1]: 'step1',
	[STEP_STATUS.step2]: 'step2',
	[STEP_STATUS.step3]: 'step3',
};

// 步骤配置信息 - 用于步骤条显示
export const STEP_CONFIG = [
	{
		key: STEP_STATUS.step1,
		title: '填写主体信息',
		stepNumber: 1,
	},
	{
		key: STEP_STATUS.step2,
		title: '实名认证授权',
		stepNumber: 2,
	},
	{
		key: STEP_STATUS.step3,
		title: '授权函签署',
		stepNumber: 3,
	},
];

// 动态导入组件函数 - 实现懒加载
const createAsyncComponent = (componentName) => {
	return defineAsyncComponent(() => import(`./${componentName}/index.vue`));
};

// 创建组件映射对象 - 自动映射所有认证状态组件
export const componentMap = Object.keys(STEP_MAP).reduce((map, status) => {
	const componentName = STEP_MAP[status];
	map[status] = createAsyncComponent(componentName);
	return map;
}, {});
