<script setup>
import { useRouter } from "vue-router";
import { orgCreditLimit } from "./api";
import { Loading } from "@element-plus/icons-vue";
import { putBusinessSubmit } from "/@/api/enterpriseInfo";

const { currentAuthStatus, AUTH_STATUS, verifyData } = inject("verify");
// 获取路由实例用于页面跳转
const router = useRouter();
// 页面数据状态管理
const pageData = ref({
  // 认证完成标题
  title: "认证完成",
  // 成功提示信息
  successMessage: "恭喜！已经完成企业信息认证",
  // 处理状态描述
  statusDescription: "",
  // 最高可贷数额
  quota: "",
  // 额度信息提示
  creditTip: "*预估额度为参考额度，最终额度以银行终审额度为准"
});
let timer = null;
const submitLoading = ref(false);
const handleGoHome = () => {
  location.href = "/";
};

const handleGoManage = () => {
  submitLoading.value = true;
  let extendedFields = verifyData.value;
  extendedFields.currentAuthStatus = AUTH_STATUS.ALLDOWN;
  extendedFields.authStatus = true; // 认证状态
  extendedFields.grantAuthStatus = true; // 授权状态
  putBusinessSubmit({ ...verifyData.value, extendedFields: JSON.stringify(extendedFields) })
    .then((res) => {
      currentAuthStatus.value = AUTH_STATUS.ALLDOWN;
      router.push("/system/business-management/pages/detail/index");
      setTimeout(()=>{
        location.reload();
      },1000)
    })
    .finally(() => {
      submitLoading.value = false;
    });
};

function getDetail() {
  orgCreditLimit().then(({ data }) => {
    if (data.highestLoanAmount) {
      pageData.value.statusDescription = "据您的企业信息，您的企业最高可贷";
      pageData.value.quota = data.highestLoanAmount;
      clearInterval(timer);
    } else {
      pageData.value.statusDescription = "根据您的企业信息，正在为您进行金融产品贷款额度的测算，请耐心等待～";
    }
  });
}

function getInfo() {
  getDetail();
  timer = setInterval(() => {
    getDetail();
  }, 2000);
}

getInfo();
onBeforeUnmount(() => {
  clearInterval(timer);
});
</script>

<template>
  <!-- 处理完成状态页面主容器 -->
  <div class="processing-complete-container">
    <!-- 状态图标区域 -->
    <div class="status-icon-section">
      <!-- 成功状态图标容器 -->
      <div class="success-icon-wrapper">
        <!-- 成功图标 - 使用在线矢量图标 -->
        <div class="success-icon">
          <svg class="icon-svg" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 0 1-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"
              fill="#52c41a"
            />
          </svg>
        </div>
        <!-- 装饰性圆点 -->
        <div class="decorative-dots">
          <div class="dot dot-1"></div>
          <div class="dot dot-2"></div>
          <div class="dot dot-3"></div>
          <div class="dot dot-4"></div>
          <div class="dot dot-5"></div>
          <div class="dot dot-6"></div>
        </div>
      </div>
    </div>

    <!-- 信息展示区域 -->
    <div class="info-section">
      <!-- 主标题 -->
      <h1 class="main-title mb-2">{{ pageData.title }}</h1>

      <!-- 成功提示信息 -->
      <p class="success-message mb-10">{{ pageData.successMessage }}</p>

      <div class="gray-block">
        <!-- 处理状态描述 -->
        <p class="description-text flex items-center">
          <div class="el-icon is-loading mr-1 " v-if="!pageData.quota">
            <el-icon>
              <Loading />
            </el-icon>
          </div>
          <span v-if="!pageData.quota">根据您的企业信息，正在为您进行金融产品贷款额度的测算，请耐心等待~~</span>
          {{ pageData.statusDescription }}
          <span v-if="pageData.quota" class="text-red-600 font-bold ml-1"> ¥ {{ pageData.quota }}</span>
        </p>

        <!-- 额度信息提示 -->
        <div class="credit-tip">
          <p class="tip-text">{{ pageData.creditTip }}</p>
        </div>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <div class="button-group">
        <el-button type="default" @click="handleGoHome">返回官网</el-button>
        <el-button type="primary" @click="handleGoManage" :loading="submitLoading">企业管理</el-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
/* 处理完成状态页面主容器 */
.processing-complete-container {
  @apply flex flex-col items-center justify-center;
  @apply px-8 py-12;
  height: 95%;
  background: #fff;
}

/* 状态图标区域 */
.status-icon-section {
  @apply mb-10;
}

/* 成功状态图标容器 */
.success-icon-wrapper {
  @apply relative flex items-center justify-center;
  width: 120px;
  height: 120px;
}

/* 成功图标样式 */
.success-icon {
  @apply relative z-10;
  width: 80px;
  height: 80px;

  .icon-svg {
    @apply w-full h-full;
    filter: drop-shadow(0 4px 12px rgba(82, 196, 26, 0.3));
  }
}

/* 装饰性圆点容器 */
.decorative-dots {
  @apply absolute inset-0;

  .dot {
    @apply absolute rounded-full;
    animation: float 3s ease-in-out infinite;
  }

  .dot-1 {
    @apply bg-green-400;
    width: 8px;
    height: 8px;
    top: 20%;
    left: 15%;
    animation-delay: 0s;
  }

  .dot-2 {
    @apply bg-blue-400;
    width: 6px;
    height: 6px;
    top: 30%;
    right: 10%;
    animation-delay: 0.5s;
  }

  .dot-3 {
    @apply bg-yellow-400;
    width: 4px;
    height: 4px;
    bottom: 25%;
    left: 20%;
    animation-delay: 1s;
  }

  .dot-4 {
    @apply bg-purple-400;
    width: 5px;
    height: 5px;
    bottom: 35%;
    right: 15%;
    animation-delay: 1.5s;
  }

  .dot-5 {
    @apply bg-pink-400;
    width: 3px;
    height: 3px;
    top: 10%;
    left: 50%;
    animation-delay: 2s;
  }

  .dot-6 {
    @apply bg-indigo-400;
    width: 4px;
    height: 4px;
    bottom: 10%;
    right: 45%;
    animation-delay: 2.5s;
  }
}

/* 浮动动画效果 */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-10px) scale(1.1);
    opacity: 1;
  }
}

/* 信息展示区域 */
.info-section {
  @apply text-center mb-16;
  .gray-block {
    @apply bg-gray-100 px-52 py-10;
  }
}

/* 主标题样式 */
.main-title {
  @apply text-3xl font-bold text-black;
  @apply tracking-wide;
}

/* 成功提示信息样式 */
.success-message {
  @apply text-lg text-black;
  @apply leading-relaxed;
}

/* 处理状态描述区域 */

.description-text {
  @apply text-black font-bold text-4xl mb-5;
  @apply leading-relaxed px-4;
}

/* 额度信息提示区域 */
.credit-tip {
  .tip-text {
    @apply text-sm text-gray-600;
    @apply leading-relaxed;
  }
}

/* 操作按钮区域 */
.action-section {
  @apply w-full max-w-md;
}

/* 按钮组容器 */
.button-group {
  @apply flex gap-4 justify-center;
}

/* 通用按钮样式 */
.action-button {
  @apply px-8 py-3 rounded-lg font-medium text-base;
  @apply transition-all duration-200 ease-in-out;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
  min-width: 120px;

  &:hover:not(:disabled) {
    @apply transform -translate-y-0.5 shadow-lg;
  }

  &.loading {
    @apply cursor-wait;
  }
}

/* 次要按钮样式 */
.secondary-button {
  @apply bg-white text-black border border-gray-300;
  @apply hover:bg-gray-50 hover:border-gray-400;
  @apply focus:ring-gray-500;
}

/* 主要按钮样式 */
.primary-button {
  @apply bg-red-500 text-white border border-red-500;
  @apply hover:bg-red-600 hover:border-red-600;
  @apply focus:ring-red-500;
}

/* 响应式设计 - 移动端适配 */
@media (max-width: 768px) {
  .processing-complete-container {
    @apply px-4 py-8;
  }

  .success-icon-wrapper {
    width: 100px;
    height: 100px;
  }

  .success-icon {
    width: 70px;
    height: 70px;
  }

  .main-title {
    @apply text-2xl mb-4;
  }

  .success-message {
    @apply text-base mb-6;
  }

  .button-group {
    @apply flex-col gap-3;
  }

  .action-button {
    @apply w-full;
  }
}

/* 高分辨率屏幕优化 */
@media (min-width: 1920px) {
  .processing-complete-container {
    @apply py-16;
  }

  .success-icon-wrapper {
    width: 140px;
    height: 140px;
  }

  .success-icon {
    width: 90px;
    height: 90px;
  }

  .main-title {
    @apply text-4xl mb-8;
  }

  .success-message {
    @apply text-xl mb-10;
  }
}
</style>
