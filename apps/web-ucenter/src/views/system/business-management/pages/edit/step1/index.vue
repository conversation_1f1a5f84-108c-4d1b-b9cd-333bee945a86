<script setup>
import company from './components/company/index.vue';
import legal from './components/legal/index.vue';
import proTitle from '@components/pro-title/index.vue';
const compantRef = ref();
const legalRef = ref();

async function getData() {
	return {
		company: await compantRef.value.getData(),
		legal: await legalRef.value.getData(),
	};
}
defineExpose({
	getData,
});
</script>

<template>
	<div>
		<proTitle title="企业信息" />
		<company ref="compantRef" />
		<proTitle title="法人信息" />
		<legal ref="legalRef" />
	</div>
</template>

<style scoped lang="scss"></style>
