<template>
	<div class="co-single-form">
		<co-form ref="formRef" :form-list="formList" :form-data="formData" :dictConfig="dictConfig" :labelWidth="labelWidth" @event="onFormEvent">
			<template #legalPersonVerifyCode="{ data }">
				<CoVerify
					class="co-verifty-wrapper"
					v-model="data.legalPersonVerifyCode"
					:verify-callback="verifyPhone"
					:captcha-api-params="{
						checkUserExist: false,
						shouldUserExist: false,
						mobile: legalPersonPhone,
						captchaKey: data.legalPersonVerifyCodeCaptchaKey,
					}"
				/>
			</template>
		</co-form>
	</div>
</template>

<script setup>
import { ref } from 'vue';
import { formListFn } from './data.js';
import * as dicApi from '@dict';
import { useDicts } from '/@/hooks/useDicts';
import { HlyIndustry } from '@dict';
import { encryptedData } from '/@/utils/encrypt';
import { ElMessage } from 'element-plus';

const { verifyData } = inject('verify');

const formRef = ref();
const formList = ref([]);
const formData = ref({});
const labelWidth = ref('178px');
const dictConfig = useDicts(dicApi, HlyIndustry);
let legalPersonPhone = ref('');
watchEffect(() => {
	formData.value = {
		...verifyData.value,
	};
	formList.value = formListFn(formData.value);
});

async function getData(isValid = true) {
	if (formRef.value) {
		let method = isValid ? 'getFormData' : 'getFormDataKey';
		let obj = formRef.value[method]({
			setIds: ['legalPersonVerifyCodeCaptchaKey'],
		});
		return obj;
	} else {
		return {};
	}
}

async function onFormEvent(val, row) {
	// 手机号
	if (val.key == 'legalPersonPhone') {
		let obj = await getData(false);
		legalPersonPhone.value = obj.legalPersonPhone;
		formRef.value.setFormDataKey('legalPersonVerifyCodeCaptchaKey', encryptedData(legalPersonPhone || ''));
	}
}

const verifyPhone = async () => {
	let obj = await getData(false);
	if (!obj.legalPersonPhone) {
		ElMessage.error('请输入手机号');
		return false;
	}
	return true;
};
defineExpose({
	getData,
});
</script>

<style lang="scss">
.co-single-form {
	.el-form {
		display: flex;
		flex-wrap: wrap;
	}

	.w-50 {
		.el-form-item__content > div {
			flex-basis: calc(50% - 87.5px);
		}
	}

	.w-30 {
		.el-form-item__content > div {
			flex-basis: 235px;
		}
	}

	.el-form-item {
		flex: 0 0 50%;
	}

	.co-form-block {
		flex: 0 0 100%;
	}

	.basis-full {
		flex-basis: 100%;
	}
}
</style>
