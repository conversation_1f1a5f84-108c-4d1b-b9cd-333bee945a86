import Pagination from '/@/components/Pagination/index.vue';
import RightToolbar from '/@/components/RightToolbar/index.vue';
import DictTag from '/@/components/DictTag/index.vue';
import UploadExcel from '/@/components/Upload/Excel.vue';
import UploadFile from '/@/components/Upload/index.vue';
import UploadImg from '/@/components/Upload/Image.vue';
import DelWrap from '/@/components/DelWrap/index.vue';
import Editor from '/@/components/Editor/index.vue';
import Tip from '/@/components/Tip/index.vue';
import TagList from '/@/components/TagList/index.vue';
import SvgIcon from '/@/components/SvgIcon/index.vue';
import { useEnvStore } from '/@/stores/env';
// 第三方组件
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import zhCn from 'element-plus/es/locale/lang/zh-cn'

import 'element-plus/dist/index.css';
import { Pane, Splitpanes } from 'splitpanes';
import 'splitpanes/dist/splitpanes.css';
// 日历组件
import { setupCalendar } from 'v-calendar';
// @ts-ignore 引入VForm3库
import VForm3 from 'form-designer-plus'; //
import 'form-designer-plus/dist/designer.style.css'; //引入VForm3样式
// 部门树组件
import vue3TreeOrg from 'vue3-tree-org';
import 'vue3-tree-org/lib/vue3-tree-org.css';

import coTable from '@components/co-table/index.js';
import pageTable from '@components/page-table';
import { getDictionary } from '/@/api/common/dictionary.js'; // 获取字典值
import { formatDic } from '/@/utils/zszc.js';

// 表单组件
import coForm from '@components/co-form';
import '@components/co-form/form.scss'

//预览
import coPreview from '@zszc/co-preview-v3';
import '@zszc/co-preview-v3/dist/index.css';

// 详情 - 返回
import proBackPre from '/@/components/pro-back-pre/index.vue';
import proSideTitle from '/@/components/pro-side-title/index.vue';
import proTitleUpDown from '/@/components/pro-title-up-down/index.vue';
import proContentTitle from '/@/components/pro-content-title/index.vue';
import proFilelist from '/@/components/pro-filelist/index.vue';
import vEleDetail from '/@/components/pro-details/index.vue';
import proDetail from '@components/pro-detail/index.vue'; // 使用栅格布局实现描述详情，vEleDetail组件内容宽度问题无法很好解决
// @ts-ignore
import coUpload from '@zszc/co-upload-v3';
// 导入声明
import { App } from 'vue';
import request from '/@/utils/request';
import { getFiles } from '/@/api/common/upload.js';
const store = useEnvStore();
const env = computed(() => store.env);
export default {
	install(app: App) {
		// app.component('coForm', coForm);
		// app.component('coFormFile', coFormFile);
		// app.component('coFormFileAll', coFormFileAll);
		// app.component('coFormPic', coFormPic);
		// 预览（等待抽离）
		// app.component('coPreview', coPreview);

		app.component('proBackPre', proBackPre);
		app.component('proSideTitle', proSideTitle);
		app.component('proTitleUpDown', proTitleUpDown);
		app.component('proContentTitle', proContentTitle);
		app.component('proFilelist', proFilelist);
		app.component('vEleDetail', vEleDetail);
		app.component('proDetail', proDetail);
		app.component('DictTag', DictTag);
		app.component('Pagination', Pagination);
		app.component('RightToolbar', RightToolbar);
		app.component('uploadExcel', UploadExcel);
		app.component('UploadFile', UploadFile);
		app.component('UploadImg', UploadImg);
		app.component('Editor', Editor);
		app.component('Tip', Tip);
		app.component('DelWrap', DelWrap);
		app.component('TagList', TagList);
		app.component('SvgIcon', SvgIcon);
		app.component('pageTable', pageTable);

		// 导入全部的elmenet-plus的图标
		for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
			app.component(key, component);
			// 兼容性
			app.component(`ele-${key}`, component);
		}
		// 导入布局插件
		app.component('Splitpanes', Splitpanes);
		app.component('Pane', Pane);
		app.use(ElementPlus,{
			locale:zhCn
		}); // ELEMENT 组件
		app.use(setupCalendar, {}); // 日历组件
		app.use(VForm3); // 表单设计
		app.use(vue3TreeOrg); // 组织架构组件
		app.use(coPreview); // 预览组件

		app.use(coForm, {
			fileSrc: function (v: string) {
				return getFiles([v]).then((res: any) => {
					const row = res.data[0];
					return { src: row.fileUrl, name: row.fileName };
				});
			},
		});

		//导入table组件
		app.use(coTable, {
			getDic: (params: any) => {
				// @ts-ignore
				return getDictionary(params).then(({ dicList }) => {
					return formatDic(dicList);
				});
			},
			attrs: {
				'header-cell-style': { backgroundColor: 'var(--el-table-row-hover-bg-color)', color: 'var(--el-text-color-primary)', align: 'left' },

				border: true,
			},
		});

		watch(env, (newValue, oldValue) => {
			// 导入上传组件
			app.use(coUpload, {
				// axios 请求实例
				axios: request,
				options: {
					// 改变上传接口请求方式，true为put，false为post，默认post
					isNotBlurRequest: true,
				},
				breakpointContinuationUploadURL: {
					// 获取文件上传列表的地址,分片上传地址在接口返回的列表中。
					chunk: (newValue as any).VUE_APP_UPLOAD_BREAK_CHUNK,
					// 上传完成并请求合并的地址
					compose: (newValue as any).VUE_APP_UPLOAD_BREAK_COMPOSE,
					VUE_APP_REPLACE_VISIT_ENDPOINT: (newValue as any).VUE_APP_REPLACE_VISIT_ENDPOINT,
				},
			});
		});
	},
};
