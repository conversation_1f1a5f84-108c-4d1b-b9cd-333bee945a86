<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<page-table :search-config="searchConfig" ref="innerTableRef" :table-config="tableConfig" :table-header="tableHeader()" @operation="onOperation">
				<template #imgId="{ row }">
					<el-image style="width: 80px; height: 80px" hide-on-click-modal :src="row.imgSrc" fit="cover" preview-teleported :preview-src-list="[row.imgSrc]" />
				</template>
			</page-table>
			<component :is="dynamicComponent" v-if="dialogVisible" v-model:visible="dialogVisible" :row="rowData" @refresh="refresh"></component>
		</div>
	</div>
</template>
<script setup>
import { searchConfig, tableConfig, tableHeader } from './data';

import { deleteById, updateStatus } from '../../api';

let dialogVisible = ref(false);
let dynamicComponent = ref('');
const edit = defineAsyncComponent(() => import('./components/edit'));
const sort = defineAsyncComponent(() => import('./components/sort'));
let innerTableRef = ref('');
let rowData = ref(null);
function refresh() {
	innerTableRef.value?.onSearchHandle();
}
// 表格操作方法
const onOperation = ({ field, row }, refresh) => {
	rowData.value = row || null;
	switch (field) {
		case 'sort':
			dialogVisible.value = true;
			dynamicComponent.value = sort;
			break;
		case 'edit':
			dialogVisible.value = true;
			dynamicComponent.value = edit;
			break;
		case 'add':
			dialogVisible.value = true;
			dynamicComponent.value = edit;
			break;
		case 'del':
			ElMessageBox.confirm('确定删除当前数据?', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					deleteById(row.id)
						.then(() => {
							ElMessage.success('操作成功');
						})
						.finally(() => {
							refresh && refresh();
						});
				})
				.catch(() => null);
			break;
		case 'disable':
			ElMessageBox.confirm('确定禁用当前数据?', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					updateStatus({ id: row.id, status: 0 })
						.then(() => {
							ElMessage.success('操作成功');
						})
						.finally(() => {
							refresh && refresh();
						});
				})
				.catch(() => null);
			break;
		case 'enable':
			ElMessageBox.confirm('确定启用当前数据?', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					updateStatus({ id: row.id, status: 1 })
						.then(() => {
							ElMessage.success('操作成功');
						})
						.finally(() => {
							refresh && refresh();
						});
				})
				.catch(() => null);
			break;
	}
};
</script>
