<template>
	<div class="layout-padding">
		<div class="layout-padding-auto layout-padding-view">
			<page-table :search-config="searchConfig" ref="innerTableRef" :table-config="tableConfig" :table-header="tableHeader()" @operation="onOperation">
				<template #orgLogoId="{ row }">
					<el-image style="width: 80px; height: 80px" hide-on-click-modal :src="row.imgSrc" fit="cover" preview-teleported :preview-src-list="[row.imgSrc]" />
				</template>
			</page-table>
			<component :is="components[dynamicComponent]" v-if="dialogVisible" :notEdit="notEdit" v-model:visible="dialogVisible" :row="rowData" @refresh="refresh"></component>
		</div>
	</div>
</template>
<script setup>
import { searchConfig, tableConfig, tableHeader } from './data';


import { deleteById, updateStatus } from '../../api';
let dynamicComponent = ref('');
const components = {
  edit : defineAsyncComponent(() => import('./components/edit'))
}
import { useRouter } from 'vue-router';

let innerTableRef = ref('');
let rowData = ref(null);
let dialogVisible = ref(false);

function refresh() {
	innerTableRef.value?.onSearchHandle();
}

const router = useRouter();
const notEdit = ref(false);
// 表格操作方法
const onOperation = ({ field, row }, refresh) => {
	rowData.value = row || null;
	notEdit.value = false;
	switch (field) {
		case 'user':
			router.push({
				path: '/financial-business/institution-user/index',
        query:{
          id: row.id,
          uniscId:row.uniscId,
          businessName:row.orgName
        }
			});
			break;
		case 'edit':
			dialogVisible.value = true;
			dynamicComponent.value = 'edit';
			break;
		case 'add':
			dialogVisible.value = true;
			dynamicComponent.value = 'edit';
			break;
		case 'detail':
			notEdit.value = true;
			dialogVisible.value = true;
			dynamicComponent.value = 'edit';
			break;
		case 'del':
			ElMessageBox.confirm('确定删除当前数据?', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					deleteById({ id: row.id })
						.then(() => {
							ElMessage.success('操作成功');
						})
						.finally(() => {
							refresh && refresh();
						});
				})
				.catch(() => null);
			break;
		case 'disable':
			ElMessageBox.confirm('确定禁用当前数据?', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					updateStatus({ id: row.id, status: 0 })
						.then(() => {
							ElMessage.success('操作成功');
						})
						.finally(() => {
							refresh && refresh();
						});
				})
				.catch(() => null);
			break;
		case 'enable':
			ElMessageBox.confirm('确定启用当前数据?', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning',
			})
				.then(() => {
					updateStatus({ id: row.id, status: 1 })
						.then(() => {
							ElMessage.success('操作成功');
						})
						.finally(() => {
							refresh && refresh();
						});
				})
				.catch(() => null);
			break;
	}
};
</script>
