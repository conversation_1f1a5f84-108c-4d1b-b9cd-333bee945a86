<template>
	<div class="layout-padding w100">
		<div class="layout-padding-auto layout-padding-view">
			<co-table ref="tableRef" :config="tableConfig" :header="tableHeader" @loaded="dsTableLoaded" @operation="onOperation" align="left">
				<template #title="{ row }">
					{{ getTitleFunc(row) }}
				</template>
			</co-table>
		</div>
		<AddDialog ref="addDialogRef" @refresh="onSearch"></AddDialog>
	</div>
</template>

<script setup lang="ts">
import { getSiteList as getSiteListApi, delObj } from '/@/api/cms/site';
import { useHandles } from '/@/hooks/handles';
const AddDialog = defineAsyncComponent(() => import('./form.vue'));
const { handleConfirm } = useHandles();

const tableRef = ref(null);

const tableHeader = [
	{ label: '序号', prop: 'id', width: 200, align: 'center' },
	{ label: '应用名称', prop: 'title', showOverflowTooltip: true },
	{ label: '站点访问域名', prop: 'domainAddress' },
];
const addDialogRef = ref();
const tableConfig = {
	request: {
		apiName: getSiteListApi,
	},
	operation: {
		fixed: 'right',
		width: 250,
	},
};

let getDataListCallback: any = null;

let onSearch: any = () => {
	getDataListCallback();
};
const dsTableLoaded = ({ getDataList }: any) => {
	getDataListCallback = getDataList;
};
const router = useRouter();
//操作
const onOperation = async (res: any) => {
	const { field, row } = res;
	switch (field) {
		case 'add':
			addDialogRef.value.openDialog();
			break;
		case 'edit':
			addDialogRef.value.openDialog(row);
			break;
		case 'config':
			router.push({
				path: '/cms/site/config/index',
				query: { id: row.id },
			});
			break;
		case 'remove':
			handleConfirm({
				texts: '此操作将永久删除, 是否继续?',
				params: [row.id],
				request: delObj,
				refresh: onSearch,
			});
			break;
		default:
			break;
	}
};
// 获取站点名称
const getTitleFunc = (row: any) => {
	if (row.title) {
		const result = row.title.split('|');
		return result.length ? result[0] : row.title;
	}
	return '-';
};
</script>

<style></style>
