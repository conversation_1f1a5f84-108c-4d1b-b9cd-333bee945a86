<template>
  <div class="co-form-image">
    <div class="image-row" v-for="item in newSrcList">
      <template v-if="item.loading">
        <icon-mod icon="Loading" size="14" color="#666" class="icon-loading is-loading"></icon-mod>
        <span class="image-tips">加载中...</span>
      </template>
      <template v-else-if="!item.info.src">
        <icon-mod icon="WarningFilled" size="17" color="#F56C6C" class="icon-filled"></icon-mod>
        <span class="image-tips image-tips-err">加载失败</span>
      </template>
      <template v-else-if="item.info.isPicture && isPreview">
        <el-image fit="contain" :src="item.info.src" :preview-src-list="[item.info.src]" :hide-on-click-modal="true" :preview-teleported="true"/>
      </template>
      <template v-else-if="item.info.isPicture">
        <el-image fit="contain" :src="item.info.src"/>
      </template>
      <template v-else>
        <icon-mod icon="Document" size="21" color="#2E5CF6" class="icon-tickets"></icon-mod>
        <div class="image-name" @click="onPreviewEvent(item.info)">{{ item.name || item.info.name }}</div>
      </template>
    </div>
    <el-image-viewer v-on:close="onClosePreview" :url-list="[previewURL]" :hide-on-click-modal="true" :teleported="true" v-if="isPreview && previewURL"/>
  </div>
</template>

<script setup>
import {ref, watch} from 'vue';
import {ElImageViewer, ElMessage, IconMod} from '../element';
import {isJSON, isArray} from '../utils/pub';
import {useFormPreview} from '../use/usePreview.js';

const props = defineProps({
  data: [String, Object, Array],
  isPreview: {type: Boolean, default: true}
});
const {previewURL, onPreview, onClosePreview, getPreviewInfo} = useFormPreview();

const newSrcList = ref([]);

watch(() => props.data, () => {
  newSrcListInit();
}, {immediate: true});

function onPreviewEvent(item) {
  if (props.isPreview) {
    onPreview(item);
  }
}

function getSrcList() {
  const val = props.data;
  if (!val) {
    return [];
  }
  if (isArray(val)) {
    return val;
  }
  if (!isJSON(val) || !isArray(val.src) && val.usrc) {
    return [val];
  }
  if (val.src) {
    return isArray(val.src) ? val.src : [val.src];
  } else {
    return [];
  }
}

// 图片列表初始化
function newSrcListInit() {
  const srcList = getSrcList();
  newSrcList.value = srcList.map((m) => {
    return isJSON(m) ? {...m, info: {}, loading: false} : {src: m, info: {}, loading: false};
  });
  newSrcList.value.forEach((m) => {
    if (!m.src) {
      return;
    }
    m.loading = true;
    // 获取文件预览信息
    getPreviewInfo(m, '' , {
      isLoad: false
    }).then(info => {
      m.info = info;
    }).catch((e) => {
      ElMessage.error(e.msg || e.message || '附件下载失败');
    }).finally(() => {
      m.loading = false;
    });
  });
}
</script>
