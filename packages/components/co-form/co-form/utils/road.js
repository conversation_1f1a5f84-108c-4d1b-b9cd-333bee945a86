import {unref} from 'vue';
import {getVal, setVal, dataFilters, dataFormat} from './other.js';
import {filtersHandle} from './otherFn.js';
import {isNull, isJSON, isArray, isFile} from './pub.js';

const defaultCSS = {
  'children': 'co-form-children', //更多
  'tips': 'co-form-tips', //文字提示
  'divider': 'co-form-divider', //横线
  'text': 'co-form-text', //文本
  'text-link': 'co-form-text-link',
  'text-image': 'co-form-text-image',
  'date': 'co-form-date', //日期
  'datetime': 'co-form-date-time', //日期时间
  'daterange': 'co-form-date-range', //关联日期
  'select': 'co-form-select', //下拉框
  'radio': 'co-form-radio', //单选
  'checkbox': 'co-form-checkbox', //复选
  'checkboxValue': 'co-form-checkbox-value', //单个复选
  'textarea': 'co-form-textarea',
  'number': 'co-form-number',
  'inputText': 'co-form-input',
  'uploadPic': 'co-form-upload-pic',
  'uploadFile': 'co-form-upload-file',
  'uploadFileAll': 'co-form-upload-file-all'
};

export function $_class(m) {
  const r = [];
  if (defaultCSS[m.type]) {
    r.push(defaultCSS[m.type]);
  }
  if (m.disabled) {
    r.push('co-form-disabled');
  }
  if (m.css) {
    r.push(m.css);
  }
  return r.join(' ');
}

// 标签
export function $_label(m, o) {
  if (m.type === 'tips' || !m.name || !o.isLabel) {
    return '';
  }
  if (m.dw && m.type !== 'text') {
    return m.name + '(' + m.dw + ')：';
  } else {
    return m.name + '：';
  }
}

// 文本信息
export function formDataFilters(row, opt) {
  const val = row.textId ? getVal(row.textId, opt.formData) : getVal(row._id, opt.newFormData);
  return dataFilters(row, val, opt.dictConfig) || row.default;
}

// 获取表单列表
export function getNewList(row, options) {
  let rs;
  if (row.dicKey) {
    rs = dataFormat(unref(options.dictConfig[row.dicKey]));
  }
  return rs || row.newList || [];
}

// 获取表单的props
export function getFormDomProps(row, options) {
  return {
    ...row.attributes,
    'placeholder': options.placeholder[row._id],
    'modelValue': options.newFormData[row._id],
    'value': options.newFormData[row._id],
    'class': !isNull(options.newFormData[row._id]) ? 'co-form-val-fill' : void (0),
    'onUpdate:modelValue': (value) => options.newFormData[row._id] = value,
    'onChange': () => options.onEmitEvent(row)
  };
}

const emptyRegEx = /^\s+|\s+$/g;

// 获取输入框的props
export function getFormInputProps(row, options) {
  return {
    ...row.attributes,
    'placeholder': options.placeholder[row._id],
    'modelValue': options.newFormData[row._id],
    'class': !isNull(options.newFormData[row._id]) ? 'co-form-val-fill' : void (0),
    'onUpdate:modelValue': (value) => {
      if (typeof value === 'string') {
        options.newFormData[row._id] = value.replace(emptyRegEx, '');
      } else {
        options.newFormData[row._id] = value;
      }
    },
    'onBlur': () => options.onEmitEvent(row)
  };
}

// 获取附件选择的props
export function getFormFileProps(row, options) {
  return {
    'options': row.upload,
    'dragger': row.dragger,
    'modelValue': options.newFormData[row._id],
    'class': !isNull(options.newFormData[row._id]) ? 'co-form-val-fill' : void (0),
    'onUpdate:modelValue': (value) => options.newFormData[row._id] = value,
    'onEvent': () => options.onFileEvent(row)
  };
}

// 第三方组件默认props
export function getFormInstallProps(item, options) {
  return {
    ...item.attributes,
    'class': !isNull(options.newFormData[item._id]) ? 'co-form-val-fill' : void 0,
    'modelValue': options.newFormData[item._id],
    'onUpdate:modelValue': (value) => {
      options.newFormData[item._id] = value;
    },
    'onChange': () => {
      options.onEmitEvent(item);
    }
  };
}

// 获取附件预览的props
export function getFormPreviewProps(row, options) {
  return {
    'data': options.getValueFn(row)
  };
}


// 写入表单附件数据
export function setFormDataFile(rdata, m, newFormData, opt) {
  let id = m.uid || m.id;
  const rv = newFormData[m._id];
  const sv = isJSON(rv) ? rv.src || rv.usrc : rv;
  // 如果附件是数组格式，则直接保存
  if (isArray(sv)) {
    setVal(rdata, id, sv);
    return;
  }
  // 如果进行isFileUid判断 && 不是file对象
  if (opt.isFileUid && !isFile(sv)) {
    id = m.id;
  }
  // 如果不是file对象，则返回传入值
  if ((opt.isFileOrigin || m.upload?.isOriginValue) && !isFile(sv)) {
    setVal(rdata, id, rv);
    return;
  }
  // 只返回file文件 || 是file对象
  if (!opt.isFile || isFile(sv)) {
    setVal(rdata, id, sv);
  }
}



// 写入表单数据
export function setFormDataInit(rdata, m, newFormData, opt, dict) {
  let id = m.uid || m.id;
  let rv = newFormData[m._id];
  // 如果是单个复选
  if (m.type === 'checkboxValue') {
    if (rv === true || rv === m.trueValue) {
      rv = isNull(m.trueValue) ? rv : m.trueValue;
    } else {
      rv = isNull(m.falseValue) ? rv : m.falseValue;
    }
  }
  const newList = m.dicKey ? dataFormat(unref(dict[m.dicKey])) || m.newList : m.newList;
  // 如果是数组
  if (newList && isArray(rv)) {
    const dl = newList.map(o => o.id);
    rv = rv.filter(o => dl.includes(o));
  }
  // setNameId: 将name添加到表单中的ID，对列表有效
  if (newList && m.setNameId) {
    const sd = isArray(rv) ? rv.map(o => filtersHandle(m, o, newList)) : filtersHandle(m, rv, newList);
    const sv = isArray(sd) && m.splitMark ? sd.join(m.splitMark) : sd;
    setVal(rdata, m.setNameId, sv);
  }
  if (isArray(rv) && m.split) {
    rv = rv.join(m.split);
  }
  if (isJSON(rv) && !isNull(rv.value)) {
    rv = rv.value;
  }
  setVal(rdata, id, rv);
}
