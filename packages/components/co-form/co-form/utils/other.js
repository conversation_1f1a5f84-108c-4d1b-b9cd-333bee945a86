import {unref} from 'vue';
import {isNull, isJSON, isArray} from './pub.js';
import {filters<PERSON>andle, verify, verifyNo} from './otherFn.js';

// 修改为下划线，为了简化多层数据处理
export function formStrTo(s) {
  return s && s.includes('.') ? s.replace(/\./g, '_') : s;
}

// 数据ID处理
export function formIdHandle(d) {
  if (!isJSON(d)) {
    return d;
  }
  d = Object.assign({}, d);
  if (d.id) {
    d._id = formStrTo(d.id);
  }
  if (d.oid) {
    d._oid = formStrTo(d.oid);
  }
  return d;
}

// 获取多层数据值
export function getVal(id, data) {
  if (!id) {
    return;
  }
  if (!id.includes('.')) {
    data = data && data[id];
  } else {
    id.split('.').forEach(function (mid) {
      data = data && data[mid];
    });
  }
  return data;
}

// 多层数据赋值
export function setVal(data, id, val) {
  if (!id) {
    return;
  }
  let da = data;
  if (!id.includes('.')) {
    da[id] = val;
    return;
  }
  const sp = id.split('.');
  const pop = sp.pop();
  sp.forEach(function (o) {
    if (!da[o]) {
      da = da[o] = {};
    } else {
      da = da[o];
    }
  });
  da[pop] = val;
}

// 表单数据处理 - list列表
export function dataFormat(data) {
  if (isJSON(data)) {
    return {id: data.id || data.value, name: data.name || data.text || data.label};
  } else if (isArray(data)) {
    return data.map(dataFormat);
  }
}

// 表单数据过滤处理
export function dataFilters(m, v, dict) {
  if (isNull(v)) {
    return m.placeholder || v;
  }
  if (isJSON(v)) {
    return v.text;
  }
  const newList = m.dicKey ? dataFormat(unref(dict[m.dicKey])) || m.newList : m.newList;
  if (newList) {
    v = isArray(v) ? v.map(o => filtersHandle(m, o, newList)).join(m.splitMark || ',') : filtersHandle(m, v, newList);
  }
  if (m.dw && !isNull(v)) {
    v += m.dw;
  }
  return v;
}

// 验证是否可以显示
export function showVerify(d, v) {
  if (typeof d.isExistHide === 'boolean') {
    return d.isExistHide ? isNull(v) : true; //当数据为空时显示
  }
  if (isNull(v)) {
    return false;
  }
  if (typeof d.val === 'function') {
    return d.val(v);
  } else if (isJSON(v)) {
    return verify(d.val, v.value) || verifyNo(d.noVal, v.value);
  } else if (isArray(v)) {
    return v.some(v2 => verify(d.val, v2)) || v.some(v2 => verifyNo(d.noVal, v2));
  } else {
    return verify(d.val, v) || verifyNo(d.noVal, v);
  }
}

const selectTips = [
  'date', 'dateTime', 'daterange',
  'select', 'radio', 'checkbox', 'checkboxValue', 'switch',
  'uploadFile', 'uploadPic', 'uploadFileAll'
];

// 表单提示信息
export function setPlaceholder(data, row) {
  if (row.placeholder) {
    data[row._id] = row.placeholder;
  } else if (selectTips.includes(row.type)) {
    data[row._id] = '请选择' + row.name;
  } else {
    data[row._id] = '请输入' + row.name;
  }
}

// 下拉菜单修改时，是否触发事件，减少不必要操作
export function setRequiredIds(data, info) {
  const fnB = function (o) {
    if (o._id) {
      data[o._id] = o.isWatch ? 2 : 1;
    }
    // oid的改变不需要触发验证事件
    if (o._oid) {
      data[o._oid] = o.isWatch ? 3 : 0;
    }
  };

  const fnA = function (a) {
    if (isJSON(a)) {
      fnB(a);
    } else if (isArray(a)) {
      a.forEach(fnB);
    }
  };
  fnA(info);
}
