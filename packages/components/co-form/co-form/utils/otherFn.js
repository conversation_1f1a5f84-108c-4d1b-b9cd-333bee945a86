import {isNull, isArray} from './pub.js';


// 表单数据处理 - list转为键值对
function dataHandle(list) {
  const r = {};
  if (list && list.length) {
    list.forEach(function (m) {
      r[m.id] = m.name;
    });
  }
  return r;
}

// 表单数据过滤处理 - value转text
export function filtersHandle(m, v, newList) {
  if (typeof m.handle === 'function') {
    return m.handle(v);
  } else if (newList) {
    return dataHandle(newList)[v];
  } else {
    return '';
  }
}

// 表单验证
export function verify(a, v) {
  if (isNull(a)) {
    return false;
  }
  if (isArray(a)) {
    return a.includes(v);
  } else {
    return a === v;
  }
}

// 表单验证 - 非
export function verifyNo(a, v) {
  if (isNull(a)) {
    return false;
  }
  if (isArray(a)) {
    return !a.includes(v);
  } else {
    return a !== v;
  }
}
