/**
 * 本地化配置管理器
 * 用于管理Element Plus组件的本地化配置
 */
import { ref } from 'vue';
import zhCn from 'element-plus/es/locale/lang/zh-cn';

// 当前本地化配置
const currentLocale = ref(zhCn);

/**
 * 设置本地化配置
 * @param {Object|String} locale - 本地化配置对象或语言代码
 */
export function setLocale(locale) {
 if (locale && typeof locale === 'object') {
    // 如果传入的是配置对象，直接使用
    currentLocale.value = locale;
  } else {
    console.warn(`传入无效配置，使用默认中文`);
    // 如果传入无效配置，使用默认中文
    currentLocale.value = zhCn;
  }
}
/**
 * 获取当前本地化配置
 * @returns {Object} 当前本地化配置
 */
export function getLocale() {
  return currentLocale.value;
}

// 默认导出当前配置的响应式引用
export default currentLocale;
