import {h, provide, inject} from 'vue';
import AsyncValidator from 'async-validator';
import {
  ElForm, ElFormItem, ElImageViewer, ElUpload,
  ElMessage, ElMessageBox, ElLoading, ElIcon,
  ElDivider, ElDatePicker, ElSwitch, ElInput,
  ElSelect, ElOption, ElRadioGroup, ElRadio, ElCheckboxGroup, ElCheckbox,
  useFormItem, ElConfigProvider
} from 'element-plus';

import {Loading, WarningFilled, Document, Plus, ZoomIn, Edit, Delete} from '@element-plus/icons-vue';
import { getLocale } from './locale/index.js';

// 这里的定义是为了解决co-form的v2.7版本差异化问题
export {
  ElForm, ElFormItem, ElImageViewer, ElUpload,
  ElMessage, ElMessageBox, ElLoading, ElIcon,
  ElDivider, ElDatePicker, ElSwitch, ElInput,
  ElSelect, ElOption, ElRadioGroup, ElRadio, ElCheckboxGroup, ElCheckbox,
  ElConfigProvider
};

// 试图获取formItem
export function getFormItem() {
  return useFormItem().formItem;
}

const iconInfo = {Loading, WarningFilled, Document, Plus, ZoomIn, Edit, Delete};

export const IconMod = {
  props: ['icon'],
  render() {
    return h(ElIcon, {}, {
      default: () => this.icon && iconInfo[this.icon] && h(iconInfo[this.icon])
    });
  }
};

/**
 * 创建带本地化配置的表单组件
 * 专门用于表单相关组件的本地化包装
 */
export function createLocalizedFormComponent(Component, props = {}, slots = {}) {
  const locale = getLocale();

  return h(ElConfigProvider, { locale }, {
    default: () => h(Component, props, slots)
  });
}

// 定义表单校验，这里使用的是element-plus的库，由element-plus引入
// 注意：childrenValidator已废弃，现在使用独立验证机制
// 每个children子字段直接绑定到自己的_id上进行独立验证
/*
export function childrenValidator(rule, value, callback) {
  const validator = new AsyncValidator(rule.verify);
  // 对数据进行校验
  validator.validate(rule.data).then(() => {
    // 校验通过
    callback();
  }).catch(({errors}) => {
    // 表单校验失败
    if (errors && errors.length) {
      callback(errors[0]);
    }
  });
}
*/
