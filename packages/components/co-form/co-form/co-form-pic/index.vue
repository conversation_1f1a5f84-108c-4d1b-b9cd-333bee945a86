<template>
  <div class="co-form-pic">
    <div class="file-container">
      <work-mod :fileInfo="fileInfo" :loading="loading" @dropEvent="onDropEvent" v-if="!props.dragger"></work-mod>
      <dragger-mod @uploadFile="onUploadFile" v-else>
        <work-mod :fileInfo="fileInfo" :loading="loading" @dropEvent="onDropEvent"></work-mod>
      </dragger-mod>
      <div class="file-icon" v-if="fileInfo.name">
        <icon-mod icon="ZoomIn" size="20" color="#fff" @click="onFilePreview()"></icon-mod>
        <icon-mod icon="Edit" size="20" color="#fff" @click="onDropEvent()"></icon-mod>
        <icon-mod icon="Delete" size="20" color="#fff" @click="onFileDelete()"></icon-mod>
      </div>
    </div>
    <div class="file-tips file-tip1" v-show="tip1 && isTips && !uploadError">{{ prompt }}</div>
    <div class="file-tips file-tip3" v-show="tip1 && isTips && uploadError">{{ uploadError }}</div>
    <input ref="inputFile" type="file" v-on:change="onUploadFile" :accept="upType.accept" style="display:none;"/>
    <el-image-viewer v-on:close="onClosePreview" :url-list="[previewURL]" :hide-on-click-modal="true" :teleported="true" v-if="previewURL"/>
  </div>
</template>

<script setup>
import {ref} from 'vue';
import {ElImageViewer, IconMod} from '../element';
import useEvent from '../co-form-file/useEvent';
import workMod from './work.vue';
import draggerMod from './dragger.vue';

const props = defineProps({
  // 参数信息 sizeMax:{Number}文件大小  upType:{Array|String}文件类型  tips:{String}补充提醒文字
  options: {type: Object, default: () => ({})},
  // v-model绑定的值
  modelValue: [Object, String, File],
  // 绑定值是否为JSON对象，默认为String
  // 在ElForm表单下，绑定值强行为JSON对象，绑定格式{src:选择的文件, error:选择文件异常提醒}
  toJSON: {type: Boolean, default: false},
  // 是否支持拖拽上传
  dragger: {type: Boolean, default: false}
});
const inputFile = ref(); //文件选择对象
const emit = defineEmits(['update:modelValue', 'event']);
const tip1 = props.options.tip1 !== false; // 是否显示上传提醒文字，默认显示

const useEventOptions = {
  componentType: 'co-form-pic',
  upTypeDefault: ['png', 'jpg', 'jpeg', 'bmp'], //上传默认类型
  sizeMaxDefault: 10,  //上传默认大小，单位：MB
  inputFile: inputFile //文件选择对象
};

const {
  onDropEvent, onUploadFile, onFileDelete, onFilePreview, onClosePreview,
  upType, prompt, isTips, uploadError, previewURL, fileInfo, loading
} = useEvent(useEventOptions, props, emit);


</script>

<style lang="scss">
@import "./index.scss";
</style>
