<template>
  <div class="dragger"
       :class="{'is-dragger': dragover}"
       @drop.prevent="$_onFileDrop"
       @dragover.prevent="$_onFileDragover"
       @dragleave.prevent="$_onFileDragleave">
    <slot/>
  </div>
</template>

<script>
export default {
  name: "dragger",
  data() {
    return {
      dragover: false
    };
  },
  methods: {
    //文件拖拽放开
    $_onFileDrop: function (e) {
      if (e.dataTransfer && e.dataTransfer.files.length) {
        this.dragover = false;
        const target = {
          value: e.dataTransfer.files[0].name,
          files: e.dataTransfer.files
        }
        this.$emit("uploadFile", {target: target})
      }
    },
    $_onFileDragover: function () {
      this.dragover = true;
    },
    $_onFileDragleave: function () {
      this.dragover = false;
    }
  }
};
</script>
