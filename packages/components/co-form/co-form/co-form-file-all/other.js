import {isNull, isJSO<PERSON>, isArray, isFile, getFileName} from '../utils/pub';

// 获取文件值
export function getValue(fileList, options) {
  const isToJSON = options.formItem ? true : options.toJSON;
  const files = fileList.map(m => {
    if (m.raw) {
      return m.raw;
    } else if (!m.isFile && options.isOriginValue) {
      return m;
    } else {
      return m.src;
    }
  });
  // 清除表单验证
  if (options.formItem && options.formItem.clearValidate) {
    options.formItem.clearValidate();
  }
  return isToJSON ? {src: files} : files;
}

// 获取文件列表
export function getDefaultValue(val) {
  if (isNull(val)) {
    return [];
  }
  // 参数归一化，数据格式处理为Array格式
  let ra;
  if (isArray(val)) {
    ra = val;
  } else if (!isJSON(val) || !isArray(val.src) && val.usrc) {
    ra = [val];
  } else if (val.src) {
    ra = isArray(val.src) ? val.src : [val.src];
  } else {
    ra = [];
  }
  return ra.map(function (m) {
    m = isJSON(m) ? m : {src: m};
    return {
      ...m,
      isFile: isFile(m.src || m.usrc),
      name: getFileName(m.usrc || m.src),
      src: m.src || m.usrc
    };
  });
}

// 文件验证
export function fileVerify(fileList, upType, sizeMax) {
  const upTypeVal = upType.value;
  const sizeMaxVal = sizeMax.value * 1024 * 1024;
  const newFileList = [];
  fileList.forEach(function (m) {
    const file = m.raw || m.src || m.usrc;
    if (!isFile(file)) {
      newFileList.push(m);
      return;
    }
    const ext = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase();
    if (!upTypeVal.includes(ext)) {
      m.err = '文件选择格式不正确';
    } else if (m.size <= 0) {
      m.err = '选择文件大小不能为0';
    } else if (m.size > sizeMaxVal) {
      m.err = '选择文件过大';
    } else {
      newFileList.push(m);
    }
  });
  const error = newFileList.length !== fileList.length ? '选择文件格式不正确或过大，已过滤' : '';
  return {
    fileList: newFileList,
    error: error
  };
}
