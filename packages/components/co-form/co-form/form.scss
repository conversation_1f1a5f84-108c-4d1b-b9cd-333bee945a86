.co-form {
  line-height: 1;

  // 对齐方式
  .el-form-item__content {
    align-items: flex-start;
  }

  // 更多表单（children组件）
  .co-form-children > .el-form-item__content {
    flex-wrap: nowrap;
    gap: 12px;

    // 子元素布局优化
    > .el-form-item {
      flex: 1;
      margin-bottom: 0;

      // 确保子元素内容不超出容器
      .el-form-item__content {
        min-width: 0; // 允许flex子项收缩
      }
    }
  }

  // 分割线
  .co-form-divider {
    .el-form-item__label {
      display: none;
    }

    .el-divider__text {
      color: #165DFF
    }
  }

  // 文本
  .co-form-text {
    white-space: pre-wrap;
    align-items: flex-start;
  }

  // 附件
  .co-form-pic,
  .co-form-file,
  .co-form-file-all {
    flex: 0 0 100%;
  }

  // 表单域标签的位置 - 上下
  .el-form--label-top {
    .el-form-item__label {
      line-height: 16px;
    }

    .co-form-upload-pic,
    .co-form-upload-file,
    .co-form-upload-file-all {
      margin-bottom: 32px;
    }
  }
}

// 附件展示
.co-form-link {
  flex: 1;
  line-height: 1.5;

  .link-row {
    width: 100%;
    padding: 4px 0;
  }

  .el-link {
    max-width: 100%;
  }

  .el-link__inner {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
  }
}

// 图片展示
.co-form-image {
  .image-row {
    width: 150px;
    height: 80px;
    border-radius: 4px;
    border: 1px dotted #C9CDD4;
    margin-right: 12px;
    vertical-align: top;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

  .el-icon {
    margin-top: 6px;
  }

  .image-tips {
    color: #86909C;
    line-height: 2;
    font-size: 13px;
  }

  .image-tips-err {
    color: #F53F3F;
  }

  .el-image {
    width: 100%;
    height: 100%;
  }

  .image-name {
    max-height: 40px;
    line-height: 15px;
    color: #165DFF;
    font-size: 13px;
    margin-top: 5px;
    padding: 0 10px;
    text-align: center;
    display: block;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-clamp: 2;
    -webkit-line-clamp: 2;
    word-break: break-all;
    word-wrap: break-word;
    white-space: normal;
    overflow-wrap: break-word;
  }

  .image-name:hover {
    text-decoration: underline;
  }

}
.zIndex-5000 {
  z-index: 5000 !important;
}
.el-image-viewer__wrapper{
  z-index: 6000 !important;
}
