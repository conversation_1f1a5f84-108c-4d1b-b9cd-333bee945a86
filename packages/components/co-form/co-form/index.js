import coForm from './form.vue';
import coFormLink from './co-form-link/index.vue';
import coFormImage from './co-form-image/index.vue';
import coFormPic from './co-form-pic/index.vue';
import coFormFile from './co-form-file/index.vue';
import coFormFileAll from './co-form-file-all/index.vue';
import defaultConfig from './config';
import {setLocale} from './locale/index.js';

function installInit(config) {
    function configMergeFn(newConfig, oidConfig) {
        for (let k in oidConfig) {
            if (typeof oidConfig[k] === 'object') {
                newConfig[k] = newConfig[k] || {};
                configMergeFn(newConfig[k], oidConfig[k]);
            } else {
                newConfig[k] = oidConfig[k];
            }
        }
    }

    // 保存属性
    configMergeFn(defaultConfig, config);
}

function install(app, config) {
    app.component('coForm', coForm);
    app.component('coFormLink', coFormLink);
    app.component('coFormImage', coFormImage);
    app.component('coFormPic', coFormPic);
    app.component('coFormFile', coFormFile);
    app.component('coFormFileAll', coFormFileAll);

    // 处理本地化配置
    setLocale(config.locale);
    installInit(config);
}

export default {install};
