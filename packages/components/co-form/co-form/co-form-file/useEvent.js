import {ref, computed, watch, toRaw} from 'vue';
import {isJSON, fileHandle} from '../utils/pub';
import {useFormPreview} from '../use/usePreview.js';
import {ElMessage, getFormItem} from '../element';

export default function useEvent(opt = {}, props, emit) {
  let saveUploadFile = null; //选择的文件
  const uploadError = ref(''); //异常提醒
  const fileInfo = ref({}); //文件信息
  const loading = ref(false); //是否正在初始化加载
  const {previewURL, onPreview, onClosePreview, onPreviewDestroy, getPreviewInfo} = useFormPreview();

  const formItem = getFormItem();

  // 定义文件支持类型
  const upType = computed(() => {
    let type = props.options.upType || opt.upTypeDefault || '';
    if (typeof type === 'string') {
      type = type.split(',');
    }
    return {
      accept: type.map(ext => ('.' + ext)).join(','),
      text: '支持格式: ' + type.join(', '),
      value: type
    };
  });

  // 定义文件选择大小
  const sizeMax = computed(() => {
    const size = props.options.sizeMax || opt.sizeMaxDefault || 10;
    const tips = size < 0.9765625 ? window.Math.floor(size * 1024) + 'KB' : size + 'MB';
    return {
      text: '最大' + tips,
      value: size
    };
  });

  // 提示信息
  const prompt = computed(() => {
    let rt = upType.value.text + '；' + sizeMax.value.text;
    if (props.options.tips) {
      rt += '；' + props.options.tips;
    }
    return rt;
  });

  // 获取表单的验证状态
  const isTips = computed(() => {
    return formItem ? formItem.validateState !== 'error' : true;
  });

  // 选择文件，点击事件
  function onDropEvent() {
    opt.inputFile.value.value = null;
    opt.inputFile.value.click();
  }

  // 文件选择完毕后处理
  function onUploadFile(event) {
    if (!event || !event.target || !event.target.value) {
      return;
    }
    // 对表单进行验证判断
    const data = fileHandle(event.target, sizeMax.value.value, upType.value.value);
    // 如果是在el-form下，则强制转化为JSON格式
    const is = formItem ? true : props.toJSON;
    // 数据赋值
    const value = saveUploadFile = (is ? {src: data.file, error: data.err} : data.file);
    // 异常提醒，如果是在表单下，会通过rules进行异常提醒，所以不需要赋值
    uploadError.value = formItem || props.options.tip3 === true ? data.err : void 0;
    // 文件预览销毁
    onPreviewDestroy();
    // 文件信息处理
    fileInfoHandle(value);
    // 修改表单值
    emit('update:modelValue', value);
    emit('event', 'select', value);
    // 清除表单验证
    if (formItem && formItem.clearValidate) {
      formItem.clearValidate();
    }
    if (data.err) {
      ElMessage.warning(data.err);
    }
  }

  // 对文件信息的处理
  function fileInfoHandle(v, isServer) {
    // 从服务器读取文件信息
    return getPreviewInfo(v, '', {
      isLoad: false,
      isServer: isServer
    }).then(info => {
      fileInfo.value = info; //保存文件信息
    }).catch(e => {
      fileInfo.value = {src: '', name: '', ext: '', isPicture: false, isPreview: false};
      if (e !== 'value-null') {
        ElMessage.error(e.message || '文件预览处理失败');
      }
    });
  }

  // 初始化执行
  function init() {
    const mv = props.modelValue;
    // 如果是在el-form下，则强制转化为JSON格式
    const is = formItem ? true : props.toJSON;
    // 如果v-model需要为Object格式 && 值不为Object格式
    if (is && mv && !isJSON(mv)) {
      const value = saveUploadFile = {src: mv};
      emit('update:modelValue', value);
    }
    // 文件预览销毁
    onPreviewDestroy();
    // 加载相关
    loading.value = true;
    // 如果是图片，初始化时，则需要从服务器读取文件信息做回显
    const isServer = opt.componentType === 'co-form-pic';
    fileInfoHandle(mv, isServer).finally(() => {
      loading.value = false;
    });
  }

  // 文件预览下载
  function onFilePreview() {
    onPreview(props.modelValue);
  }

  // 文件删除
  function onFileDelete() {
    // 文件预览销毁
    onPreviewDestroy();
    // 如果是在el-form下，则强制转化为JSON格式
    const is = formItem ? true : props.toJSON;
    // 重新赋值
    const value = is ? {src: void (0)} : void (0);
    emit('update:modelValue', value);
    emit('event', 'select', value);
  }

  // 初始化执行，验证
  init();

  // 如果传入的参数有变化
  watch(() => props.modelValue, function (v) {
    if (saveUploadFile !== toRaw(v)) {
      uploadError.value = null; //取消异常提醒
      init();
    }
  });

  return {
    onDropEvent, onUploadFile, onFileDelete, onFilePreview, onClosePreview,
    upType, prompt, isTips, uploadError, previewURL, fileInfo, loading
  };
}
