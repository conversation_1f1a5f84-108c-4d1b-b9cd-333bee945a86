<template>
  <div class="co-form-file">
    <div class="file-drop" @click="onDropEvent()">
      <el-button :type="uploadStyle" v-if="isBtn">{{ fileName ? text2 : text1 }}</el-button>
      <span class="file-name" v-else-if="isName">{{ fileName ? fileName : text1 }}</span>
      <span class="file-name" v-else>{{ fileName ? text2 : text1 }}</span>
    </div>
    <div class="file-operate">
      <div class="file-name file-preview" @click="onFilePreview()" v-if="fileName">
        <span>预览下载</span>
      </div>
      <div class="file-name file-preview file-tip3" @click="onFileDelete()" v-if="isDel && fileName">
        <span>删除</span>
      </div>
    </div>
    <!-- 文件模版 -->
    <div class="file-name download-name" @click="onDownload()" v-if="download.src">
      <img src="./icon/download.svg" alt="icon"/>
      <span>{{ download.name }}</span>
    </div>
    <template v-if="isAlwaysTip">
      <!-- 上传提醒 -->
      <div class="file-tips file-tip1" v-show="isTips && tip1">{{ prompt }}</div>
    </template>
    <template v-else>
      <!-- 上传提醒 -->
      <div class="file-tips file-tip1 " v-show="isTips && tip1 && !fileName && !uploadError">{{ prompt }}</div>
      <!-- 文件名称 -->
      <div class="file-tips file-tip2" v-show="isTips && tip2 && fileName && !uploadError && isBtn">{{ fileName }}</div>
      <!-- 异常提醒 -->
      <div class="file-tips file-tip3" v-show="isTips && tip3 && uploadError">{{ uploadError }}</div>
    </template>
    <input ref="inputFile" type="file" v-on:change="onUploadFile" :accept="upType.accept" style="display:none;"/>
    <el-image-viewer v-on:close="onClosePreview" :url-list="[previewURL]" :hide-on-click-modal="true" :teleported="true" v-if="previewURL"/>
  </div>
</template>

<script setup>
import {ref, computed} from 'vue';
import {ElImageViewer} from '../element';
import {isJSON, downloadFile} from '../utils/pub';
import useEvent from './useEvent';

const props = defineProps({
  // 参数信息
  // options.sizeMax:{Number}文件大小
  // options.upType:{Array|String}文件类型
  // options.tips:{String}补充提醒文字
  options: {type: Object, default: () => ({})},
  // v-model绑定的值
  modelValue: [Object, String, File],
  // v-model绑定的值绑定值是否为Object格式，默认为String
  // 在ElForm表单下，绑定值强行为Object格式，格式为{src:选择的文件, error:选择文件异常提醒}
  toJSON: {type: Boolean, default: false},
  // 是否支持拖拽上传
  dragger: {type: Boolean, default: false}
});

const inputFile = ref(); // 文件选择对象
const emit = defineEmits(['update:modelValue', 'event']);
const tip1 = props.options.tip1 !== false; // 是否显示上传提醒文字，默认显示
const tip2 = props.options.tip2 !== false; // 是否显示上传文件名称，默认显示
const tip3 = props.options.tip3 === true; // 是否显示异常信息提醒，默认不显示
const isAlwaysTip = props.options.isAlwaysTip === true; //是否一直显示上传提醒文字，默认否
const isBtn = props.options.isBtn !== false; // 附件选择是否是button按钮，默认button按钮
const isName = props.options.isName !== false; // 文字选择按钮是否显示文件名，默认显示
const isDel = props.options.isDel !== false; // 是否允许删除选择文件，默认允许
const isPre = props.options.isPre !== false; // 是否允许预览选择文件，默认允许
const text1 = props.options.t1 || '请选择文件'; // 文件未选择时的提示文字
const text2 = props.options.t2 || '文件已选择'; // 文件选择后的提醒文字

const useEventOptions = {
  componentType: 'co-form-file',
  upTypeDefault: ['zip', 'rar', 'xls', 'xlsx', 'doc', 'docx', 'pdf', 'png', 'jpg', 'jpeg', 'bmp'], //上传默认类型
  sizeMaxDefault: 10, //上传默认大小，单位：MB
  inputFile: inputFile //文件选择对象
};

const {
  onDropEvent, onUploadFile, onFilePreview, onFileDelete, onClosePreview,
  upType, prompt, isTips, uploadError, previewURL, fileInfo
} = useEvent(useEventOptions, props, emit);

const fileName = computed(() => {
  return fileInfo.value.name;
});

const uploadStyle = computed(() => {
  return fileName.value ? 'success' : 'primary';
});

const download = computed(() => {
  const down = props.options.download;
  return isJSON(down) ? down : {src: down, name: '文件下载'};
});

// 下载模版
function onDownload() {
  downloadFile(download.value.src);
}
</script>

<style lang="scss">
@import "./index.scss";
</style>
