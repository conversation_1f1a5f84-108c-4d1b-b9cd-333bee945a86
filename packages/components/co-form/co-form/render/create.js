import {h} from 'vue';
import {isNull} from '../utils/pub.js';
import * as road from '../utils/road.js';
import {ElFormItem} from '../element.js';
import {hasRequiredChildren} from '../utils/verify.js';
import domFn from './dom-del.js';
import {domInstall} from './dom-del.js';
import defaultConfig from '../config.js';

// 创建节点
function newDome(row, sk, options, slots) {
  if (sk) {
    return slots[sk] ? slots[sk]({
      row: row,
      data: options.newFormData,
      value: options.newFormData[row._id],
      css: !isNull(options.newFormData[row._id]) ? 'co-form-val-fill' : void (0)
    }) : void (0);
  }
  // 判断是否存在
  const components = defaultConfig.components[row.type];
  // 如果存在第三方组件
  if (components) {
    return domInstall(row, options, components);
  }

  if (domFn[row.type]) {
    return domFn[row.type](row, options);
  } else {
    return domFn.default(row, options);
  }
}

export function createDom(item, newOptions, slots) {
  const style = newOptions.showFn(item.relation, item) ? void (0) : {display: 'none'};

  // 如果是children字段，为每个子字段创建独立的FormItem
  if (item.children) {
    const childrenFormItems = [];
    item.children.forEach(item2 => {
      if (newOptions.showFn(item2.relation, item2)) {
        // 为每个子字段创建独立的FormItem，支持独立验证
        // 子字段不显示label，使用父级的label
        const childFormItem = h(ElFormItem, {
          key: item2._id,
          prop: item2._id,
          label: '', // 子字段不显示label
          class: road.$_class(item2),
          style: newOptions.showFn(item2.relation, item2) ? void (0) : {display: 'none'}
        }, {
          default: () => [newDome(item2, item2.slot, newOptions, slots)]
        });
        childrenFormItems.push(childFormItem);
      }
    });

    // 检测children中是否包含必填子字段
    const isRequired = hasRequiredChildren(item.children, newOptions);

    // 返回包含所有子FormItem的父容器
    // 注意：父级FormItem不设置prop，避免验证消息叠加，只有子字段参与验证
    return h(ElFormItem, {
      key: item.key,
      // prop: item.key, // 移除prop，避免父级容器参与验证导致消息叠加
      label: road.$_label(item, newOptions),
      class: road.$_class(item),
      style: style,
      required: isRequired // 如果包含必填子字段，则父级显示必填标识
    }, {
      default: () => childrenFormItems
    });
  } else {
    // 普通字段的处理逻辑保持不变
    const dom = [];
    dom.push(newDome(item, item.slot, newOptions, slots));

    if (item.rowSlot) {
      dom.push(newDome(item, item.rowSlot, newOptions, slots));
    }

    return h(ElFormItem, {
      key: item.key,
      prop: item.key,
      label: road.$_label(item, newOptions),
      class: road.$_class(item),
      style: style
    }, {
      default: () => dom
    });
  }
}
