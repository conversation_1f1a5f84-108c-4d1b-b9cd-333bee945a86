import {ref, unref, h} from 'vue';
import {ElForm, ElConfigProvider} from '../element';
import {useForm} from '../use/useForm.js';
import {useExpose} from '../use/useExpose.js';
import {createDom} from './create.js';
import { getLocale } from '../locale/index.js';

export default function setup(props, {slots, emit, expose}) {
  const formRef = ref(); //定义formRef
  const pubOptions = {
    formItemInit: void (0)
  };
  // 表单
  const {
    newFormList,
    newFormData,
    newFormRules,
    placeholder,
    labelWidth,
    getValueFn,
    showFn,
    onEmitEvent,
    onFileEvent,
    onFormValidate
  } = useForm({
    pubOptions,
    formRef
  }, props, emit);

  // 创建dom节点
  // const {
  //   formItemList
  // } = useCreate({
  //   pubOptions,
  //   newFormList,
  //   newFormData,
  //   placeholder,
  //   getValueFn, //获取表单数据
  //   showFn, //表单项显示处理函数
  //   onEmitEvent, //回调事件
  //   onFileEvent //附件选择后触发
  // }, props, slots);

  // 生成表单行节点
  // if (pubOptions.formItemInit) {
  //   pubOptions.formItemInit();
  // }

  // 暴露函数
  useExpose({newFormList, newFormData, formRef, showFn}, props, expose);

  // 返回数据
  return () => {

    const newOptions = {
      isLabel: unref(props.isLabel),
      newFormData: newFormData.value,
      placeholder: placeholder.value,
      dictConfig: props.dictConfig || {},
      formData: props.formData,
      getValueFn: getValueFn, //获取表单数据
      showFn: showFn, //表单项显示处理函数
      onEmitEvent: onEmitEvent, //回调事件
      onFileEvent: onFileEvent //附件选择后触发
    };

    // form节点
    const formItemDom = unref(newFormList).map(item => {
      return createDom(item, newOptions, slots)
    });

    // 获取当前本地化配置
    const locale = getLocale();

    // form表单 - 包装在ConfigProvider中以确保本地化配置生效
    const formDom = h(ElConfigProvider, { locale }, {
      default: () => h(ElForm, {
        'ref': formRef,
        'model': unref(newFormData),
        'label-width': unref(labelWidth),
        'label-position': props.labelPosition,
        'size': props.size,
        'inline': props.inline,
        'rules': unref(newFormRules),
        'validate-on-rule-change': false,
        'onValidate': onFormValidate
      }, {
        default: () => formItemDom
      })
    });

    return h('div', {class: 'co-form'}, [formDom]);
  };
}
