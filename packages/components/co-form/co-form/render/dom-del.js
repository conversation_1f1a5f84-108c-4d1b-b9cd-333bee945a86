import {h, resolveComponent} from 'vue';
import {
  ElDivider, ElDatePicker, ElSwitch, ElInput, ElIcon,
  ElSelect, ElOption, ElRadioGroup, ElRadio, ElCheckboxGroup, ElCheckbox,
  createLocalizedFormComponent, IconMod
} from '../element';
import CoFormLink from '../co-form-link/index.vue';
import CoFormImage from '../co-form-image/index.vue';
import CoFormPic from '../co-form-pic/index.vue';
import CoFormFile from '../co-form-file/index.vue';
import CoFormFileAll from '../co-form-file-all/index.vue';
import * as road from '../utils/road.js';

/**
 * 创建输入框插槽内容（prepend/append）
 * @param {string|object} content - 插槽内容配置
 * @returns {VNode|VNode[]} Vue虚拟节点
 */
function createInputSlotContent(content) {
  if (!content) return null;

  // 如果是字符串，直接返回文本节点
  if (typeof content === 'string') {
    return content;
  }

  // 如果是对象，处理图标和文本的组合
  if (typeof content === 'object') {
    const elements = [];

    // 处理图标
    if (content.icon) {
      try {
        // 尝试解析全局注册的图标组件
        const IconComponent = resolveComponent(content.icon);
        elements.push(h(ElIcon, {}, {default: () => h(IconComponent)}));
      } catch (error) {
        // 如果图标不存在，回退到 IconMod 组件
        console.warn(`图标 "${content.icon}" 未找到，请检查图标名称是否正确`);
        elements.push(h(IconMod, {icon: content.icon}));
      }
    }

    // 处理文本
    if (content.text) {
      elements.push(content.text);
    }

    // 如果只有一个元素，直接返回；多个元素用span包装并添加flex样式
    if (elements.length === 1) {
      return elements[0];
    } else if (elements.length > 1) {
      return h('span', {
        style: {
          display: 'flex',
          alignItems: 'center',
          gap: '4px'
        }
      }, elements);
    }
  }

  return null;
}

export default {
  // 文本提示
  'tips': function (row) {
    return h('div', void (0), row.name);
  },
  // 横线
  'divider': function (row) {
    return h(ElDivider, row.attributes);
  },
  // 文本
  'text': function (row, options) {
    return h('div', void (0), road.formDataFilters(row, options));
  },
  // 附件链接
  'text-link': function (row, options) {
    return h(CoFormLink, road.getFormPreviewProps(row, options));
  },
  // 图片链接
  'text-image': function (row, options) {
    return h(CoFormImage, road.getFormPreviewProps(row, options));
  },
  // 日期
  'date': function (row, options) {
    return createLocalizedFormComponent(ElDatePicker, road.getFormDomProps(row, options));
  },
  // 日期时间
  'datetime': function (row, options) {
    return createLocalizedFormComponent(ElDatePicker, road.getFormDomProps(row, options));
  },
  // 关联日期
  'daterange': function (row, options) {
    return createLocalizedFormComponent(ElDatePicker, road.getFormDomProps(row, options));
  },
  // 下拉框
  'select': function (row, options) {
    const newList = road.getNewList(row, options);
    const optionList = newList.map(m => h(ElOption, {key: m.id, label: m.name, value: m.id}));
    return createLocalizedFormComponent(ElSelect, road.getFormDomProps(row, options), {default: () => optionList});
  },
  // 单选
  'radio': function (row, options) {
    const newList = road.getNewList(row, options);
    const optionList = newList.map(m => h(ElRadio, {key: m.id, label: m.id, value: m.id}, () => m.name));
    return h(ElRadioGroup, road.getFormDomProps(row, options), {default: () => optionList});
  },
  // 复选
  'checkbox': function (row, options) {
    const newList = road.getNewList(row, options);
    const optionList = newList.map(m => h(ElCheckbox, {key: m.id, label: m.id, value: m.id}, () => m.name));
    return h(ElCheckboxGroup, road.getFormDomProps(row, options), {default: () => optionList});
  },
  // 单个复选
  'checkboxValue': function (row, options) {
    return h(ElCheckbox, road.getFormDomProps(row, options), {default: () => row.checkLabel});
  },
  // Switch 开关
  'switch': function (row, options) {
    return h(ElSwitch, road.getFormDomProps(row, options));
  },
  // 文本框、多行文本框
  'default': function (row, options) {
    const props = road.getFormInputProps(row, options);
    const slots = {};

    // 处理 prepend 前置内容
    if (row.prepend) {
      slots.prepend = () => createInputSlotContent(row.prepend);
    }

    // 处理 append 后置内容
    if (row.append) {
      slots.append = () => createInputSlotContent(row.append);
    }

    return h(ElInput, props, slots);
  },
  // 图片选择
  'uploadPic': function (row, options) {
    return h(CoFormPic, road.getFormFileProps(row, options));
  },
  // 附件选择
  'uploadFile': function (row, options) {
    return h(CoFormFile, road.getFormFileProps(row, options));
  },
  // 多附件选择
  'uploadFileAll': function (row, options) {
    return h(CoFormFileAll, road.getFormFileProps(row, options));
  }
};


// 安装第三方组件
export function domInstall(item, options, components) {
  let component;
  if (typeof components.component === 'string') {
    component = resolveComponent(components.component);
  } else if (typeof components.component === 'function') {
    component = components.component();
  }
  if (!component) {
    return h('div', '第三方组件传入格式不正确，只能为字符串或函数');
  }
  if (components.propsFn) {
    return h(component, components.propsFn(item, options));
  } else {
    return h(component, road.getFormInstallProps(item, options));
  }
}
