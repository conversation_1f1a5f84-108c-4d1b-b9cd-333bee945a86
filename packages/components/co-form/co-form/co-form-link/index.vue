<template>
  <div class="co-form-link">
    <div class="link-row" v-for="item in srcList">
      <el-link type="primary" @click="onPreviewEvent(item)">{{ getName(item) }}</el-link>
    </div>
    <el-image-viewer v-on:close="onClosePreview" :url-list="[previewURL]" :hide-on-click-modal="true" :teleported="true" v-if="isPreview && previewURL"/>
  </div>
</template>

<script setup>
import {computed} from 'vue';
import {ElImageViewer} from '../element';
import {isArray, isJSON, getFileName} from '../utils/pub';
import {useFormPreview} from '../use/usePreview.js';

const props = defineProps({
  data: [String, Object, Array],
  isPreview: {type: Boolean, default: true}
});
const {previewURL, onPreview, onClosePreview} = useFormPreview();

const srcList = computed(() => {
  const val = props.data;
  if (!val) {
    return [];
  }
  if (isArray(val)) {
    return val;
  }
  if (!isJSON(val) || !isArray(val.src) && val.usrc) {
    return [val];
  }
  if (val.src) {
    return isArray(val.src) ? val.src : [val.src];
  } else {
    return [];
  }
});

function onPreviewEvent(item) {
  if (props.isPreview) {
    onPreview(item);
  }
}

// 获取文件名
function getName(item) {
  if (props.fileName) {
    return getFileName(props.fileName);
  }
  const rn = isJSON(item) ? item.name || getFileName(item.usrc || item.src) : getFileName(item);
  return rn || '预览下载';
}
</script>
