## 更新记录 v3.1.3 - 更新日期 2025.05.16

### 添加功能

```
1. 附件添加提示相关
2. 添加函数setFormDataAll，以JOSN格式修改newFormData
3. getFormData传入参数修改，添加field字段，验证指定的ID，格式Array|string
4. 附件验证时，添加验证提示修改字段msg，代替默认提示
5. 表单验证时，添加isRequired，当为true时，去掉默认不验证规则判断
6. 添加字段setRelation，获取表单数据时，改变隐藏表单不返回数据的特性；false：不改变，默认；true: 改变
7. co-form-file 添加isPre和isAlwaysTip参数
7.1 isPre：是否允许预览选择文件，默认允许
7.2 isAlwaysTip：是否一直显示上传提醒文字，默认否
```

### 解决 BUG

```
1. 引入组件时，config设置options无效问题
2. 当name为空，lable显示undefined: 
3. co-form-file-all表单组件，删除选择附件时，无回调问题
```

### 相关优化

```
1. utils/other.js 和 utils/verify.js 修改，优化验证规则提示
```

### 删除

```
1. 删除type=area，地市选择组件的引入
```
