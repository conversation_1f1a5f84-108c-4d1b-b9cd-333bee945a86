## 更新记录v3.1.2 - 更新日期2024.12.30

### 添加功能
```
1. co-form 添加功能，children支持relation选项
2. co-form 添加功能，表单内容存在时，添加co-form-val-fill类
3. co-form 添加功能，表单内容存在时，插槽添加css回调参数，值为co-form-val-fill
4. co-form 新抛出setValidateField方法 可重新验证某id的rule
```

### 解决BUG
```
1. 当type为checkboxValue时，未定义trueValue或者falseValue，造成返回结果为空的问题，未定义时应返回boolean值
2. 当required为Object或者Array时，validate验证无效的问题
3. 引入组件时，config设置options无效问题
```

### 优化相关
```
1.css颜色值根据规范要求修改
```