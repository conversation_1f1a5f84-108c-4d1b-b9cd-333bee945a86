## 更新记录 v3.1.8 - 更新日期 2025.07

### 更新功能

```
1. 输入框支持前置和后置图标
使用如下： 
    {id: 'price', name: '商品价格', prepend: '￥', append: '元'},
    {id: 'search', name: '搜索内容', prepend: {icon: 'Search'}, append: {text: '搜索', icon: 'Search'}}
2.拓展验证数字的功能，支持整数和小数位数的限制，以及最大值和最小值限制，使用方式如下：
{
  id: 'fieldId',
  name: '字段名称',
  validate: {
    type: 'number',              // 验证类型，必须为 'number'
    int: 8,                      // 整数位数限制（可选，默认10位）
    decimal: 2,                  // 小数位数限制（可选，默认2位）
    minus: true,                 // 是否允许负数（可选，默认false）
    min: 0,                      // 最小值（可选）
    max: 99999999.99            // 最大值（可选）
  }
}

```

### 解决 BUG

```

```
