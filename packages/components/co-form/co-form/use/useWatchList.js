import {onUnmounted, watch} from 'vue';

export function useWatchList(options, props) {

  let watchList = [];

  // 创建侦听器
  function createWatchList(data) {
    clearWatchList();
    Object.keys(data).forEach(function (id) {
      const dv = data[id];
      let watchFn;
      if (dv === 2) {
        watchFn = watch(() => props.shareData[id], formRulesInit);
      } else if (dv === 3) {
        watchFn = watch(() => props.formData[id], formRulesInit);
      }
      if (watchFn) {
        watchList.push(watchFn);
      }
    });
  }

  let $setTimeout;

  // 创建表单验证信息
  function formRulesInit() {
    if ($setTimeout) {
      clearTimeout($setTimeout);
      $setTimeout = null;
    }
    $setTimeout = setTimeout(() => {
      // 清除任务
      $setTimeout = null;
      //重新修改表单验证信息
      options.newFormRules.value = options.getNewFormRules();
      //表单验证提示清除
      options.clearValidate();
    }, 0);
  }

  // 销毁侦听器
  function clearWatchList() {
    watchList.forEach(function (watchFn) {
      watchFn();
    });
    watchList = [];
  }

  // vue卸载，销毁侦听器
  onUnmounted(function () {
    clearWatchList();
  });

  return {createWatchList};
}
