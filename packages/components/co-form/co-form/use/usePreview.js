import {ref, onUnmounted} from 'vue';
import {isJSON, isFile, isExternal, getFileSrc, downloadFile, downloadText} from '../utils/pub.js';
import {ElLoading, ElMessage} from '../element';

// 文件预览
export function useFormPreview() {
  const previewURL = ref();
  // 保存文件预览对象
  let savePreviewFile = {};

  /**
   * 获取文件预览信息 - 服务器读取
   * @param {Object|String} row 预览参数
   * @param {String} [name] 文件名，可不传
   * @param {Object} [opt] 请求选项
   * @param {Boolean} [opt.isLoad] 是否显示loading动画，默认显示
   * @param {Boolean} [opt.isServer] 当不是File对象和外链时，是否从服务器获取数据，默认获取
   * @returns {Promise}
   */
  function getPreviewInfo(row, name, opt = {}) {
    row = isJSON(row) ? row : {src: row};
    const src = row.raw || row.src || row.usrc;
    const usrc = name || row.name || row.usrc || row.src;
    if (!src) {
      return Promise.reject('value-null');
    }
    // 如果不是File对象 并且 也不是外链地址，则需要保存
    const isSave = !isFile(src) && !isExternal(src) && opt.isServer !== false;
    if (isSave && savePreviewFile[src]) {
      return Promise.resolve(savePreviewFile[src]);
    }
    let loading;
    if (opt.isLoad !== false) {
      loading = ElLoading.service({lock: true, text: '加载中...', customClass: 'el-loading-white', background: 'rgba(0, 0, 0, 0.7)'});
    }
    return getFileSrc(src, usrc, opt.isServer).then((info = {}) => {
      if (isSave) {
        savePreviewFile[src] = info;
      }
      return info;
    }).finally(() => {
      loading && loading.close();
    });
  }

  // 文件预览
  function onPreview(row, name) {
    // 获取文件预览信息
    getPreviewInfo(row, name).then(info => {
      if (info.isPicture) {
        previewURL.value = info.src;
      } else if (info.isPreview) {
        window.open(info.src);
      } else if (info.isText) {
        downloadText(info.src, info.name);
      } else {
        downloadFile(info.src, info.name);
      }
    }).catch((e) => {
      if (e !== 'value-null') {
        ElMessage.error(e.message || '文件预览下载失败');
      }
    });
  }

  // 文件销毁
  function onPreviewDestroy() {
    const data = savePreviewFile;
    for (let k in data) {
      if (data[k].src) {
        URL.revokeObjectURL(data[k].src);
      }
    }
    savePreviewFile = {};
  }

  // 文件预览销毁
  onUnmounted(function () {
    onPreviewDestroy();
  });

  // 关闭图片预览
  function onClosePreview() {
    previewURL.value = '';
  }

  return {previewURL, onPreview, onClosePreview, onPreviewDestroy, getPreviewInfo};
}
